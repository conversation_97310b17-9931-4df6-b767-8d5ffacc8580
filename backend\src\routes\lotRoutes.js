/**
 * Routes simplifiées pour la gestion des lots
 * Version optimisée sans référence à Espece ou Prise
 */
const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const Lot = require('../models/Lot');
const Veterinaire = require('../models/Veterinaire');
const Maryeur = require('../models/Maryeur');
const Client = require('../models/Client');
const { auth, checkRole } = require('../middleware/auth');
const { NotFoundError, BadRequestError } = require('../middleware/errorHandler');
const notificationService = require('../services/notificationService');
const websocketService = require('../services/websocketService');

/**
 * @route GET /api/lots
 * @desc Récupérer tous les lots
 * @access Public
 */
router.get('/', async (req, res, next) => {
  try {
    // Filtres optionnels
    const filter = {};

    // Filtrer par statut de vente si spécifié
    if (req.query.vendu !== undefined) {
      filter.vendu = req.query.vendu === 'true';
    }

    // Filtrer par nom d'espèce si spécifié
    if (req.query.especeNom) {
      filter.especeNom = req.query.especeNom;
    }

    // Filtrer par vétérinaire si spécifié
    if (req.query.veterinaire) {
      filter.veterinaire = req.query.veterinaire;
    }

    // Filtrer par mareyeur si spécifié
    if (req.query.maryeur) {
      filter.maryeur = req.query.maryeur;
    }

    // Filtrer par utilisateur (pêcheur) si spécifié
    if (req.query.user) {
      filter.user = req.query.user;
    }

    // Exécuter la requête avec les filtres
    const lots = await Lot.find(filter)
      .populate('veterinaire', 'nom prenom')
      .populate('maryeur', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    // Standardiser la réponse
    res.success({ data: lots }, 'Liste des lots récupérée avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/lots
 * @desc Créer un nouveau lot - Version simplifiée
 * @access Private (Pêcheur, Mareyeur ou Admin)
 */
router.post('/', auth, checkRole(['ROLE_PECHEUR', 'ROLE_MARYEUR', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    console.log(`[${new Date().toISOString()}] INFO [LOT] Création d'un nouveau lot - Données reçues:`, req.body);

    // Générer un identifiant unique si non fourni
    if (!req.body.identifiant) {
      req.body.identifiant = `LOT-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    }

    // Extraire les données de base du lot
    const lotData = {
      // Utiliser les données reçues ou des valeurs par défaut
      identifiant: req.body.identifiant,
      photo: req.body.photo || req.body.imageUrl || null,
      especeNom: req.body.especeNom || 'Poisson',
      poids: req.body.poids || 0,
      temperature: req.body.temperature || 0,
      lieu: req.body.lieu || req.body.emplacement || 'Non spécifié',
      description: req.body.description || null,
      // ✅ SUPPRIMÉ: quantite (le poids est suffisant)

      // Statuts initiaux
      isValidated: false,
      vendu: false,
      isAuctionActive: false,
      test: false,
      status: false,

      // Date de soumission
      dateSoumission: new Date()
    };

    // Traiter les références (IDs)
    // Utilisateur (pêcheur)
    if (req.user && req.user._id) {
      lotData.user = req.user._id;
    } else if (req.body.user || req.body.pecheur) {
      lotData.user = req.body.user || req.body.pecheur;
    }

    // Vétérinaire
    if (req.body.veterinaire) {
      lotData.veterinaire = req.body.veterinaire;
    }

    // Mareyeur
    if (req.body.maryeur) {
      lotData.maryeur = req.body.maryeur;
    }

    console.log(`[${new Date().toISOString()}] INFO [LOT] Données préparées pour la création:`, lotData);

    // Créer un nouvel objet lot
    const lot = new Lot(lotData);

    try {
      // Sauvegarder le lot dans la base de données
      await lot.save();

      console.log(`[${new Date().toISOString()}] INFO [LOT] Lot créé avec succès:`, {
        id: lot._id,
        identifiant: lot.identifiant,
        photo: lot.photo ? 'présente' : 'absente',
        especeNom: lot.especeNom
      });

      // Envoyer une notification au vétérinaire si spécifié
      if (lot.veterinaire) {
        try {
          await notificationService.sendNotification({
            destinataire: lot.veterinaire,
            type: 'NOUVEAU_LOT',
            titre: 'Nouveau lot à analyser',
            message: `Un nouveau lot (${lot.identifiant}) a été soumis pour analyse`,
            data: { lotId: lot._id }
          });
          console.log(`[${new Date().toISOString()}] INFO [LOT] Notification envoyée au vétérinaire ${lot.veterinaire}`);
        } catch (notifError) {
          console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification:`, notifError);
          // Ne pas bloquer la création du lot si la notification échoue
        }
      }

      // Retourner une réponse de succès
      return res.status(201).json({
        success: true,
        message: 'Lot créé avec succès',
        lot: {
          id: lot._id,
          identifiant: lot.identifiant,
          photo: lot.photo,
          especeNom: lot.especeNom,
          poids: lot.poids,
          temperature: lot.temperature,
          lieu: lot.lieu,
          veterinaire: lot.veterinaire,
          maryeur: lot.maryeur,
          user: lot.user,
          dateSoumission: lot.dateSoumission
        }
      });

    } catch (error) {
      console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de la création du lot:`, error);

      // Formater l'erreur pour une meilleure lisibilité
      let errorMessage = 'Erreur lors de la création du lot';
      let statusCode = 500;

      if (error.name === 'ValidationError') {
        errorMessage = 'Validation échouée: ' + Object.values(error.errors).map(e => e.message).join(', ');
        statusCode = 400;
      } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
        if (error.code === 11000) {
          errorMessage = 'Un lot avec cet identifiant existe déjà';
          statusCode = 409;
        } else {
          errorMessage = 'Erreur de base de données: ' + error.message;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      return res.status(statusCode).json({
        success: false,
        message: errorMessage,
        error: error.message
      });
    }
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de la création du lot:`, error);

    // Formater l'erreur pour une meilleure lisibilité
    let errorMessage = 'Erreur lors de la création du lot';
    let statusCode = 500;

    if (error.name === 'ValidationError') {
      errorMessage = 'Validation échouée: ' + Object.values(error.errors).map(e => e.message).join(', ');
      statusCode = 400;
    } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
      if (error.code === 11000) {
        errorMessage = 'Un lot avec cet identifiant existe déjà';
        statusCode = 409;
      } else {
        errorMessage = 'Erreur de base de données: ' + error.message;
      }
    } else if (error.message) {
      errorMessage = error.message;
    }

    return res.status(statusCode).json({
      success: false,
      message: errorMessage,
      error: error.message
    });
  }
});

/**
 * @route GET /api/lots/featured
 * @desc Récupérer les lots en vedette pour la page d'accueil
 * @access Public
 */
router.get('/featured', async (req, res, next) => {
  try {
    // Récupérer les lots qui ont un prix initial, qui sont validés par un vétérinaire et qui ne sont pas vendus
    const filter = {
      prixInitial: { $exists: true, $ne: null },
      test: true,
      status: true,
      vendu: false
    };

    // Limiter à 5 lots maximum, triés par date de soumission (les plus récents d'abord)
    const lots = await Lot.find(filter, {
      // Projection: sélectionner uniquement les champs nécessaires
      identifiant: 1,
      photo: 1,
      especeNom: 1,
      prixInitial: 1,
      prixMinimal: 1,
      dateSoumission: 1,
      veterinaire: 1,
      user: 1
    })
      .sort({ dateSoumission: -1 })
      .limit(5)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .lean(); // Convertir en objets JavaScript simples pour de meilleures performances

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: lots }, 'Liste des lots en vedette récupérée avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/lots/available
 * @desc Récupérer tous les lots disponibles pour enchères avec pagination
 * @access Public
 */
router.get('/available', async (req, res, next) => {
  try {
    // Récupérer les paramètres de pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Récupérer les lots qui ont un prix initial, qui sont validés par un vétérinaire et qui ne sont pas vendus
    const filter = {
      prixInitial: { $exists: true, $ne: null },
      test: true,
      status: true,
      vendu: false
    };

    // Filtrer par nom d'espèce si spécifié
    if (req.query.especeNom) {
      filter.especeNom = { $regex: req.query.especeNom, $options: 'i' };
    }

    // Compter le nombre total de lots pour la pagination
    const total = await Lot.countDocuments(filter);

    // Trier par date de soumission (les plus récents d'abord) avec pagination
    const lots = await Lot.find(filter, {
      // Projection: sélectionner uniquement les champs nécessaires
      identifiant: 1,
      photo: 1,
      especeNom: 1,
      prixInitial: 1,
      prixMinimal: 1,
      dateSoumission: 1,
      veterinaire: 1,
      user: 1,
      vendu: 1,
      test: 1,
      status: 1
    })
      .sort({ dateSoumission: -1 })
      .skip(skip)
      .limit(limit)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .lean(); // Convertir en objets JavaScript simples pour de meilleures performances

    // Ajouter les informations de pagination à la réponse
    res.success({
      data: lots,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    }, 'Liste des lots disponibles récupérée avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/lots/pending
 * @desc Récupérer tous les lots en attente de validation par un vétérinaire
 * @access Private (Vétérinaire ou Admin)
 */
router.get('/pending', auth, checkRole(['ROLE_VETERINAIRE', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    console.log(`[${new Date().toISOString()}] INFO [LOT] Récupération des lots en attente pour l'utilisateur: ${req.user._id}, rôles: ${req.user.roles}`);

    // Récupérer les lots qui n'ont pas encore été testés par un vétérinaire
    // et qui ont un utilisateur (pêcheur) associé et des informations valides
    // Assurons-nous que isValidated est explicitement à false pour éviter les lots déjà traités
    let filter = {
      test: false,
      isValidated: false, // S'assurer que le lot n'a pas déjà été validé
      user: { $exists: true, $ne: null }, // S'assurer que le lot a un pêcheur associé
      especeNom: { $exists: true, $ne: null }, // S'assurer que le lot a une espèce associée
      photo: { $exists: true, $ne: null } // S'assurer que le lot a une photo
    };

    // Si l'utilisateur est un vétérinaire, ne montrer que les lots qui lui sont assignés
    // Cette modification est essentielle pour que chaque vétérinaire ne voie que ses lots
    if (req.user.roles.includes('ROLE_VETERINAIRE')) {
      console.log(`[${new Date().toISOString()}] INFO [LOT] Filtrage des lots pour le vétérinaire: ${req.user._id}`);
      filter.veterinaire = req.user._id;
    }

    // Code redondant supprimé - le filtrage est déjà fait ci-dessus

    console.log(`[${new Date().toISOString()}] INFO [LOT] Filtre utilisé pour la recherche des lots: ${JSON.stringify(filter)}`);

    // Trier par date de soumission (les plus récents d'abord)
    const lots = await Lot.find(filter)
      .sort({ dateSoumission: -1 })
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom');

    console.log(`[${new Date().toISOString()}] INFO [LOT] Nombre de lots en attente trouvés: ${lots.length}`);

    // Afficher les détails des lots pour le débogage
    if (lots.length > 0) {
      lots.forEach((lot, index) => {
        console.log(`[${new Date().toISOString()}] INFO [LOT] Lot #${index + 1}: ID=${lot._id}, Espèce=${lot.especeNom || 'Non spécifiée'}, Vétérinaire=${lot.veterinaire ? lot.veterinaire._id : 'Non assigné'}`);
      });
    } else {
      console.log(`[${new Date().toISOString()}] INFO [LOT] Aucun lot en attente trouvé pour ce vétérinaire`);
    }

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: lots }, 'Liste des lots en attente récupérée avec succès');
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de la récupération des lots en attente:`, error);
    next(error);
  }
});

/**
 * @route GET /api/lots/pending-price
 * @desc Récupérer tous les lots validés par un vétérinaire mais sans prix initial
 * @access Private (Maryeur ou Admin)
 */
router.get('/pending-price', auth, checkRole(['ROLE_MARYEUR', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    // Récupérer les lots qui ont été validés par un vétérinaire mais qui n'ont pas encore de prix initial
    // Ajouter des critères de validation pour éviter les lots avec des données invalides
    const filter = {
      test: true,
      status: true,
      isValidated: true,
      $or: [
        { prixInitial: { $exists: false } },
        { prixInitial: null }
      ],
      vendu: false,
      // Critères de validation des données
      user: { $exists: true, $ne: null },
      especeNom: { $exists: true, $ne: null, $ne: '', $ne: 'Poisson' },
      dateSoumission: { $exists: true, $ne: null },
      photo: { $exists: true, $ne: null }
    };

    // Trier par date de soumission (les plus récents d'abord)
    const lots = await Lot.find(filter)
      .sort({ dateSoumission: -1 })
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom');

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: lots }, 'Liste des lots en attente de prix récupérée avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/lots/maryeur/:id
 * @desc Récupérer tous les lots associés à un maryeur
 * @access Private (Maryeur ou Admin)
 */
router.get('/maryeur/:id', auth, checkRole(['ROLE_MARYEUR', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    let filter = {};

    // Vérifier si l'ID est un ObjectId valide
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      // Filtrer directement par l'ID du maryeur
      filter.maryeur = req.params.id;
    } else {
      // Si ce n'est pas un ObjectId, chercher le maryeur par ID personnalisé
      const Maryeur = require('../models/Maryeur');
      const maryeur = await Maryeur.findOne({ id: req.params.id });

      if (!maryeur) {
        // Standardiser la réponse pour être cohérent avec les autres routes
        return res.success({ data: [] }, 'Aucun lot trouvé pour ce maryeur');
      }

      // Filtrer directement par l'ID du maryeur
      filter.maryeur = maryeur._id;
    }

    // Ajouter des filtres supplémentaires si fournis

    // Filtrer par statut de vente si spécifié
    if (req.query.vendu !== undefined) {
      filter.vendu = req.query.vendu === 'true';
    }

    // Filtrer par statut de test si spécifié
    if (req.query.status !== undefined) {
      filter.status = req.query.status === 'true';
    }

    // Filtrer par prix initial si spécifié
    if (req.query.hasPrixInitial !== undefined) {
      if (req.query.hasPrixInitial === 'true') {
        filter.prixInitial = { $exists: true, $ne: null };
      } else {
        filter.prixInitial = { $exists: false };
      }
    }

    // Filtrer par date de soumission
    if (req.query.dateDebut) {
      if (!filter.dateSoumission) filter.dateSoumission = {};
      filter.dateSoumission.$gte = new Date(req.query.dateDebut);
    }

    if (req.query.dateFin) {
      if (!filter.dateSoumission) filter.dateSoumission = {};
      filter.dateSoumission.$lte = new Date(req.query.dateFin);
    }

    // Récupérer tous les lots associés à ce maryeur avec les filtres appliqués
    const lots = await Lot.find(filter)
      .sort({ dateSoumission: -1 }) // Trier par date de soumission (les plus récents d'abord)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: lots }, 'Liste des lots du maryeur récupérée avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/lots/pecheur/:id
 * @desc Récupérer tous les lots associés à un pêcheur
 * @access Public
 */
router.get('/pecheur/:id', async (req, res, next) => {
  try {
    let filter = {};

    // Vérifier si l'ID est un ObjectId valide
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      // Filtrer directement par l'ID du pêcheur
      filter.user = req.params.id;
    } else {
      // Si ce n'est pas un ObjectId, chercher le pêcheur par ID personnalisé
      const Pecheur = require('../models/Pecheur');
      const pecheur = await Pecheur.findOne({ id: req.params.id });

      if (!pecheur) {
        return res.success({ data: [] }, 'Aucun lot trouvé pour ce pêcheur');
      }

      // Filtrer directement par l'ID du pêcheur
      filter.user = pecheur._id;
    }

    // Récupérer tous les lots associés à ce pêcheur - Version simplifiée
    const lots = await Lot.find(filter)
      .populate('veterinaire', 'nom prenom')
      .populate('maryeur', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: lots }, 'Liste des lots du pêcheur récupérée avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/lots/search
 * @desc Rechercher des lots par nom d'espèce avec pagination
 * @access Public
 */
router.get('/search', async (req, res, next) => {
  try {
    const query = req.query.query;

    // Récupérer les paramètres de pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    if (!query) {
      return res.success({
        data: [],
        pagination: {
          total: 0,
          page,
          limit,
          pages: 0
        }
      }, 'Aucun terme de recherche fourni');
    }

    // Rechercher directement dans les lots par nom d'espèce
    const filter = {
      especeNom: { $regex: query, $options: 'i' },
      prixInitial: { $exists: true, $ne: null },
      test: true,
      status: true,
      vendu: false
    };

    // Compter le nombre total de lots pour la pagination
    const total = await Lot.countDocuments(filter);

    // Rechercher les lots avec pagination
    const lots = await Lot.find(filter)
      .sort({ dateSoumission: -1 })
      .skip(skip)
      .limit(limit)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .lean();

    // Ajouter les informations de pagination à la réponse
    res.success({
      data: lots,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    }, 'Résultats de recherche récupérés avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/auctions/maryeur/:id
 * @desc Récupérer les enchères actives d'un maryeur
 * @access Private (Maryeur ou Admin)
 */
router.get('/auctions/maryeur/:id', auth, checkRole(['ROLE_MARYEUR', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    let filter = {};

    // Vérifier si l'ID est un ObjectId valide
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      // Filtrer directement par l'ID du maryeur, avec prix initial et non vendus
      filter = {
        maryeur: req.params.id,
        prixInitial: { $exists: true, $ne: null },
        vendu: false,
        test: true,
        status: true
      };
    } else {
      // Si ce n'est pas un ObjectId, chercher le maryeur par ID personnalisé
      const Maryeur = require('../models/Maryeur');
      const maryeur = await Maryeur.findOne({ id: req.params.id });

      if (!maryeur) {
        return res.success({ data: [] }, 'Aucune enchère trouvée pour ce maryeur');
      }

      // Filtrer directement par l'ID du maryeur, avec prix initial et non vendus
      filter = {
        maryeur: maryeur._id,
        prixInitial: { $exists: true, $ne: null },
        vendu: false,
        test: true,
        status: true
      };
    }

    // Récupérer toutes les enchères actives associées à ce maryeur
    const lots = await Lot.find(filter)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: lots }, 'Liste des enchères actives du maryeur récupérée avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/lots/veterinaire/:id
 * @desc Récupérer tous les lots associés à un vétérinaire
 * @access Private (Vétérinaire ou Admin)
 */
router.get('/veterinaire/:id', auth, checkRole(['ROLE_VETERINAIRE', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    let filter = {};

    // Vérifier si l'ID est un ObjectId valide
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      filter.veterinaire = req.params.id;
    } else {
      // Si ce n'est pas un ObjectId, chercher le vétérinaire par ID personnalisé
      const Veterinaire = require('../models/Veterinaire');
      const veterinaire = await Veterinaire.findOne({ id: req.params.id });

      if (!veterinaire) {
        return res.success([], 'Aucun lot trouvé pour ce vétérinaire');
      }

      filter.veterinaire = veterinaire._id;
    }

    // Ajouter des filtres supplémentaires si fournis

    // Filtrer par statut de test si spécifié
    if (req.query.status !== undefined) {
      filter.status = req.query.status === 'true';
    }

    // Filtrer par date de test
    if (req.query.dateDebut) {
      if (!filter.dateTest) filter.dateTest = {};
      filter.dateTest.$gte = new Date(req.query.dateDebut);
    }

    if (req.query.dateFin) {
      if (!filter.dateTest) filter.dateTest = {};
      filter.dateTest.$lte = new Date(req.query.dateFin);
    }

    // Récupérer tous les lots associés à ce vétérinaire avec les filtres appliqués
    const lots = await Lot.find(filter)
      .sort({ dateTest: -1 }) // Trier par date de test (les plus récents d'abord)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: lots }, 'Liste des lots du vétérinaire récupérée avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/lots/:id
 * @desc Récupérer un lot spécifique
 * @access Public
 */
router.get('/:id', async (req, res, next) => {
  try {
    let lot;

    // Essayer de trouver par ObjectId (MongoDB ID)
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      lot = await Lot.findById(req.params.id)
        .populate('veterinaire', 'nom prenom')
        .populate('user', 'nom prenom')
        .populate('acheteur', 'nom prenom');
    }

    // Si non trouvé, essayer de trouver par identifiant
    if (!lot) {
      lot = await Lot.findOne({ identifiant: req.params.id })
        .populate('veterinaire', 'nom prenom')
        .populate('user', 'nom prenom')
        .populate('acheteur', 'nom prenom');
    }

    // Si non trouvé, essayer de trouver par RFID
    if (!lot) {
      lot = await Lot.findOne({ rfid: req.params.id })
        .populate('veterinaire', 'nom prenom')
        .populate('user', 'nom prenom')
        .populate('acheteur', 'nom prenom');
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    res.success(lot, 'Lot récupéré avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/lots/:id/bid
 * @desc Placer une enchère sur un lot avec mise à jour en temps réel
 * @access Private (Client uniquement)
 */
router.post('/:id/bid', auth, checkRole('ROLE_CLIENT'), async (req, res, next) => {
  try {
    const { amount, userId } = req.body;

    if (!amount || amount <= 0) {
      return res.error('Le montant de l\'enchère doit être supérieur à 0', 400);
    }

    // Vérifier que l'utilisateur est bien un client
    const Client = require('../models/Client');
    let client;

    if (mongoose.Types.ObjectId.isValid(userId)) {
      client = await Client.findById(userId);
    } else {
      client = await Client.findOne({ id: userId });
    }

    if (!client) {
      return res.error('Client non trouvé', 404);
    }

    // Récupérer le lot
    let lot;
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      lot = await Lot.findById(req.params.id);
    } else {
      lot = await Lot.findOne({ identifiant: req.params.id });
    }

    if (!lot) {
      return res.error('Lot non trouvé', 404);
    }

    // Vérifier que le lot est disponible pour enchères
    if (!lot.prixInitial || !lot.test || !lot.status || lot.vendu) {
      return res.error('Ce lot n\'est pas disponible pour enchères', 400);
    }

    // Vérifier que l'enchère est active
    if (!lot.isAuctionActive) {
      return res.error('Cette enchère n\'est plus active', 400);
    }

    // Vérifier que la date de fin n'est pas dépassée
    const now = new Date();
    if (lot.dateFinEnchere && lot.dateFinEnchere < now) {
      return res.error('Cette enchère est terminée', 400);
    }

    // Vérifier que l'enchère est supérieure au prix initial ou à l'enchère actuelle
    const prixActuel = lot.prixEnchere || lot.prixInitial;
    if (amount <= prixActuel) {
      return res.error(`L'enchère doit être supérieure au prix actuel (${prixActuel})`, 400);
    }

    // Mettre à jour le lot avec la nouvelle enchère
    lot.prixEnchere = amount;
    lot.acheteur = client._id;
    lot.dateEnchere = new Date();

    await lot.save();

    // Récupérer le lot mis à jour avec les relations peuplées
    const populatedLot = await Lot.findById(lot._id)
      .populate('acheteur', 'nom prenom');

    // Notifier tous les clients participant à l'enchère de la nouvelle offre
    try {
      // Récupérer tous les clients
      const clients = await Client.find({ isValidated: true, isBlocked: false });

      // Envoyer une notification à chaque client
      for (const c of clients) {
        // Ne pas notifier le client qui vient de placer l'enchère
        if (c._id.toString() !== client._id.toString()) {
          await notificationService.notifierClient(
            c._id,
            'Nouvelle offre sur une enchère',
            `Une nouvelle offre de ${amount} dinars a été placée sur le lot ${populatedLot.identifiant} de ${lot.especeNom || 'poisson'}.`,
            'info',
            {
              reference: lot._id,
              referenceModel: 'Lot',
              urlAction: `/client/auctions/${lot._id}`
            }
          );
        }
      }

      // Notifier le mareyeur de la nouvelle offre
      if (lot.maryeur) {
        await notificationService.notifierMaryeur(
          lot.maryeur,
          'Nouvelle offre sur une enchère',
          `Une nouvelle offre de ${amount} dinars a été placée sur le lot ${populatedLot.identifiant} de ${lot.especeNom || 'poisson'} par ${populatedLot.acheteur ? populatedLot.acheteur.nom + ' ' + populatedLot.acheteur.prenom : 'un client'}.`,
          'info',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/maryeur/lots/${lot._id}`
          }
        );
      }
    } catch (error) {
      console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi des notifications:`, error);
    }

    // Envoyer une mise à jour en temps réel à tous les clients connectés
    try {
      // Créer un objet avec les informations de l'enchère
      const auctionUpdate = {
        lotId: lot._id,
        identifiant: lot.identifiant,
        prixEnchere: lot.prixEnchere,
        acheteur: populatedLot.acheteur ? {
          id: populatedLot.acheteur._id,
          nom: populatedLot.acheteur.nom,
          prenom: populatedLot.acheteur.prenom
        } : null,
        dateEnchere: lot.dateEnchere
      };

      // Envoyer la mise à jour à tous les clients via WebSocket
      const websocketService = require('../services/websocketService');
      websocketService.broadcastToAll('auction_update', auctionUpdate);
    } catch (error) {
      console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la mise à jour en temps réel:`, error);
    }

    res.success(lot, 'Enchère placée avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route PATCH /api/lots/:id
 * @desc Mettre à jour un lot
 * @access Private (Mareyeur, Vétérinaire ou Admin)
 */
router.patch('/:id', auth, checkRole(['ROLE_MARYEUR', 'ROLE_VETERINAIRE', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    let lot;

    // Essayer de mettre à jour par ObjectId (MongoDB ID)
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      lot = await Lot.findByIdAndUpdate(req.params.id, req.body, {
        new: true,
        runValidators: true
      });
    }

    // Si non trouvé, essayer de mettre à jour par identifiant
    if (!lot) {
      lot = await Lot.findOneAndUpdate({ identifiant: req.params.id }, req.body, {
        new: true,
        runValidators: true
      });
    }

    // Si non trouvé, essayer de mettre à jour par RFID
    if (!lot) {
      lot = await Lot.findOneAndUpdate({ rfid: req.params.id }, req.body, {
        new: true,
        runValidators: true
      });
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    // Récupérer le lot mis à jour avec les relations peuplées - Version simplifiée
    const populatedLot = await Lot.findById(lot._id)
      .populate('veterinaire', 'nom prenom')
      .populate('maryeur', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    res.success(populatedLot, 'Lot mis à jour avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/lots/:id
 * @desc Supprimer un lot
 * @access Private (Admin uniquement)
 */
router.delete('/:id', auth, checkRole('ROLE_ADMIN'), async (req, res, next) => {
  try {
    let lot;

    // Essayer de supprimer par ObjectId (MongoDB ID)
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      lot = await Lot.findByIdAndDelete(req.params.id);
    }

    // Si non trouvé, essayer de supprimer par identifiant
    if (!lot) {
      lot = await Lot.findOneAndDelete({ identifiant: req.params.id });
    }

    // Si non trouvé, essayer de supprimer par RFID
    if (!lot) {
      lot = await Lot.findOneAndDelete({ rfid: req.params.id });
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    res.success(null, 'Lot supprimé avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/lots/:id/approve
 * @desc Approuver un lot par un vétérinaire
 * @access Private (Vétérinaire ou Admin)
 */
router.put('/:id/approve', auth, checkRole(['ROLE_VETERINAIRE', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    console.log(`[${new Date().toISOString()}] INFO [LOT] Approbation du lot ${req.params.id} par le vétérinaire ${req.user._id}`);

    // Mettre à jour les données du test
    const updateData = {
      test: true,
      dateTest: new Date(),
      veterinaire: req.user._id,
      status: true,
      isValidated: true // ✅ CORRECTION : Marquer comme validé
    };

    // Ajouter la température si fournie
    if (req.body.temperature) {
      updateData.temperature = req.body.temperature;
    }

    // Si un mareyeur est fourni dans la requête, l'utiliser
    if (req.body.maryeur) {
      console.log(`[${new Date().toISOString()}] INFO [LOT] Mareyeur fourni dans la requête: ${req.body.maryeur}`);
      updateData.maryeur = req.body.maryeur;
    }

    // Mettre à jour le lot
    let lot = await Lot.findById(req.params.id);

    if (!lot) {
      // Si non trouvé par ID, essayer par identifiant
      lot = await Lot.findOne({ identifiant: req.params.id });
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    // Vérifier si le vétérinaire qui approuve est bien celui assigné au lot
    if (lot.veterinaire && lot.veterinaire.toString() !== req.user._id.toString() && !req.user.roles.includes('ROLE_ADMIN')) {
      console.log(`[${new Date().toISOString()}] WARN [LOT] Tentative d'approbation par un vétérinaire non assigné: ${req.user._id}, vétérinaire assigné: ${lot.veterinaire}`);
      // On permet quand même l'approbation, mais on log l'avertissement
    }

    // Journaliser l'état du lot avant la mise à jour
    console.log(`[${new Date().toISOString()}] INFO [LOT] État du lot avant approbation:`, {
      id: lot._id,
      identifiant: lot.identifiant,
      maryeur: lot.maryeur,
      veterinaire: lot.veterinaire
    });

    // Appliquer les mises à jour
    Object.assign(lot, updateData);
    await lot.save();
    console.log(`[${new Date().toISOString()}] INFO [LOT] Lot ${lot._id} approuvé avec succès`);

    // Récupérer le lot mis à jour avec les relations peuplées
    const populatedLot = await Lot.findById(lot._id)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom');

    // Vérifier si le lot a un mareyeur assigné directement
    let maryeurId = lot.maryeur;

    // Journaliser l'état du lot après la mise à jour
    console.log(`[${new Date().toISOString()}] INFO [LOT] État du lot après approbation:`, {
      id: lot._id,
      identifiant: lot.identifiant,
      maryeur: lot.maryeur,
      veterinaire: lot.veterinaire
    });

    // Notifier le mareyeur du résultat du test
    if (maryeurId) {
      try {
        console.log(`[${new Date().toISOString()}] INFO [LOT] Envoi de notification au mareyeur: ${maryeurId}`);

        await notificationService.notifierMaryeur(
          maryeurId,
          `Lot validé par le vétérinaire`,
          `Le lot ${populatedLot.identifiant} de ${populatedLot.especeNom || 'poisson'} a été validé par le vétérinaire et est prêt pour la mise en vente.`,
          'success',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/maryeur/lots/${lot._id}`
          }
        );
        console.log(`[${new Date().toISOString()}] INFO [LOT] Notification envoyée avec succès au mareyeur`);
      } catch (error) {
        console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification au mareyeur:`, error);
      }
    } else {
      console.log(`[${new Date().toISOString()}] WARN [LOT] Aucun mareyeur trouvé pour le lot ${lot._id}`);
    }

    // Notifier le pêcheur du résultat du test - Version simplifiée
    if (populatedLot.user) {
      try {
        console.log(`[${new Date().toISOString()}] INFO [LOT] Envoi de notification au pêcheur: ${populatedLot.user._id}`);

        await notificationService.notifierPecheur(
          populatedLot.user._id,
          `Lot validé par le vétérinaire`,
          `Le lot ${populatedLot.identifiant} de ${populatedLot.especeNom || 'poisson'} a été validé par le vétérinaire et transmis au mareyeur.`,
          'success',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/pecheur/lots/${lot._id}`
          }
        );
        console.log(`[${new Date().toISOString()}] INFO [LOT] Notification envoyée avec succès au pêcheur`);
      } catch (error) {
        console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification au pêcheur:`, error);
      }
    } else {
      console.log(`[${new Date().toISOString()}] WARN [LOT] Aucun pêcheur trouvé pour le lot ${lot._id}`);
    }

    res.success(populatedLot, 'Lot approuvé avec succès');
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'approbation du lot:`, error);
    next(error);
  }
});

/**
 * @route PUT /api/lots/:id/reject
 * @desc Rejeter un lot par un vétérinaire
 * @access Private (Vétérinaire ou Admin)
 */
router.put('/:id/reject', auth, checkRole(['ROLE_VETERINAIRE', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    // Mettre à jour les données du test
    const updateData = {
      test: true,
      dateTest: new Date(),
      veterinaire: req.user._id,
      status: false
    };

    // Ajouter la température si fournie
    if (req.body.temperature) {
      updateData.temperature = req.body.temperature;
    }

    // Ajouter le motif de refus si fourni
    if (req.body.motif) {
      updateData.motifRefus = req.body.motif;
      updateData.raisonRejet = req.body.motif; // ✅ CORRECTION : Ajouter pour compatibilité frontend
      console.log(`[${new Date().toISOString()}] INFO [LOT] Motif de refus: ${req.body.motif}`);
    }

    // Si un mareyeur est fourni dans la requête, l'utiliser
    if (req.body.maryeur) {
      console.log(`[${new Date().toISOString()}] INFO [LOT] Mareyeur fourni dans la requête: ${req.body.maryeur}`);
      updateData.maryeur = req.body.maryeur;
    }

    // Mettre à jour le lot
    let lot = await Lot.findById(req.params.id);

    if (!lot) {
      // Si non trouvé par ID, essayer par identifiant
      lot = await Lot.findOne({ identifiant: req.params.id });
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    // Journaliser l'état du lot avant la mise à jour
    console.log(`[${new Date().toISOString()}] INFO [LOT] État du lot avant rejet:`, {
      id: lot._id,
      identifiant: lot.identifiant,
      maryeur: lot.maryeur,
      veterinaire: lot.veterinaire
    });

    // Appliquer les mises à jour
    Object.assign(lot, updateData);
    await lot.save();
    console.log(`[${new Date().toISOString()}] INFO [LOT] Lot ${lot._id} rejeté avec succès`);

    // Récupérer le lot mis à jour avec les relations peuplées - Version simplifiée
    const populatedLot = await Lot.findById(lot._id)
      .populate('veterinaire', 'nom prenom')
      .populate('maryeur', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    // Vérifier si le lot a un mareyeur assigné directement
    let maryeurId = lot.maryeur;

    // Version simplifiée - Pas besoin de vérifier dans la prise

    // Journaliser l'état du lot après la mise à jour
    console.log(`[${new Date().toISOString()}] INFO [LOT] État du lot après rejet:`, {
      id: lot._id,
      identifiant: lot.identifiant,
      maryeur: lot.maryeur,
      veterinaire: lot.veterinaire
    });

    // Notifier le mareyeur du résultat du test
    if (maryeurId) {
      try {
        console.log(`[${new Date().toISOString()}] INFO [LOT] Envoi de notification au mareyeur: ${maryeurId}`);

        await notificationService.notifierMaryeur(
          maryeurId,
          `Lot refusé par le vétérinaire`,
          `Le lot ${populatedLot.identifiant} de ${populatedLot.especeNom || 'poisson'} a été refusé par le vétérinaire.${lot.motifRefus ? ` Motif: ${lot.motifRefus}` : ''}`,
          'warning',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/maryeur/lots/${lot._id}`
          }
        );
        console.log(`[${new Date().toISOString()}] INFO [LOT] Notification envoyée avec succès au mareyeur`);
      } catch (error) {
        console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification au mareyeur:`, error);
      }
    } else {
      console.log(`[${new Date().toISOString()}] WARN [LOT] Aucun mareyeur trouvé pour le lot ${lot._id}`);
    }

    // Notifier le pêcheur du résultat du test - Version simplifiée
    if (populatedLot.user) {
      try {
        await notificationService.notifierPecheur(
          populatedLot.user._id,
          `Lot refusé par le vétérinaire`,
          `Le lot ${populatedLot.identifiant} de ${populatedLot.especeNom || 'poisson'} a été refusé par le vétérinaire.${lot.motifRefus ? ` Motif: ${lot.motifRefus}` : ''}`,
          'warning',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/pecheur/lots/${lot._id}`
          }
        );
      } catch (error) {
        console.error('Erreur lors de l\'envoi de la notification au pêcheur:', error);
      }
    }

    res.success(populatedLot, 'Lot refusé avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route PATCH /api/lots/:id/test
 * @desc Valider un lot par un vétérinaire
 * @access Private (Vétérinaire ou Admin)
 */
router.patch('/:id/test', auth, checkRole(['ROLE_VETERINAIRE', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    // Vérifier que les données nécessaires sont présentes
    if (req.body.test === undefined) {
      throw new BadRequestError('Le résultat du test est requis');
    }

    // Mettre à jour les données du test
    const updateData = {
      test: req.body.test,
      dateTest: new Date(),
      veterinaire: req.user._id,
      temperature: req.body.temperature,
      status: req.body.status !== undefined ? req.body.status : true,
      isValidated: req.body.test === true // ✅ CORRECTION : Marquer comme validé si test=true
    };

    // Ajouter le motif de refus si fourni
    if (req.body.motif) {
      updateData.motifRefus = req.body.motif;
      updateData.raisonRejet = req.body.motif; // ✅ CORRECTION : Ajouter pour compatibilité frontend
    }

    let lot;

    // Essayer de mettre à jour par ObjectId (MongoDB ID)
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      lot = await Lot.findByIdAndUpdate(req.params.id, updateData, {
        new: true,
        runValidators: true
      });
    }

    // Si non trouvé, essayer de mettre à jour par identifiant
    if (!lot) {
      lot = await Lot.findOneAndUpdate({ identifiant: req.params.id }, updateData, {
        new: true,
        runValidators: true
      });
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    // Récupérer le lot mis à jour avec les relations peuplées - Version simplifiée
    const populatedLot = await Lot.findById(lot._id)
      .populate('veterinaire', 'nom prenom')
      .populate('maryeur', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    // Notifier le mareyeur du résultat du test - Version simplifiée
    if (populatedLot.maryeur) {
      try {
        const resultat = req.body.test ? 'validé' : 'refusé';
        await notificationService.notifierMaryeur(
          populatedLot.maryeur._id,
          `Lot ${resultat} par le vétérinaire`,
          `Le lot ${populatedLot.identifiant} de ${populatedLot.especeNom || 'poisson'} a été ${resultat} par le vétérinaire.`,
          req.body.test ? 'success' : 'warning',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/maryeur/lots/${lot._id}`
          }
        );
      } catch (error) {
        console.error('Erreur lors de l\'envoi de la notification au mareyeur:', error);
      }
    }

    // Notifier le pêcheur du résultat du test - Version simplifiée
    if (populatedLot.user) {
      try {
        const resultat = req.body.test ? 'validé' : 'refusé';
        await notificationService.notifierPecheur(
          populatedLot.user._id,
          `Lot ${resultat} par le vétérinaire`,
          `Le lot ${populatedLot.identifiant} de ${populatedLot.especeNom || 'poisson'} a été ${resultat} par le vétérinaire.`,
          req.body.test ? 'success' : 'warning',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/pecheur/lots/${lot._id}`
          }
        );
      } catch (error) {
        console.error('Erreur lors de l\'envoi de la notification au pêcheur:', error);
      }
    }

    res.success(populatedLot, 'Test du lot effectué avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/lots/:id/auction/start
 * @desc Démarrer une enchère pour un lot (durée fixe de 10 minutes)
 * @access Private (Mareyeur ou Admin)
 */
router.put('/:id/auction/start', auth, checkRole(['ROLE_MARYEUR', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    // Définir la date de début (maintenant) et la date de fin (dans 10 minutes)
    const now = new Date();
    const dateDebutEnchere = now;
    const dateFinEnchere = new Date(now.getTime() + 10 * 60 * 1000); // 10 minutes en millisecondes

    // Récupérer le lot
    let lot;
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      lot = await Lot.findById(req.params.id);
    } else {
      lot = await Lot.findOne({ identifiant: req.params.id });
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    // Vérifier que le lot a un prix initial défini
    if (!lot.prixInitial) {
      throw new BadRequestError('Ce lot n\'a pas de prix initial défini');
    }

    // Vérifier que le lot a été validé par un vétérinaire
    if (!lot.test || !lot.status) {
      throw new BadRequestError('Le lot doit être validé par un vétérinaire avant de démarrer une enchère');
    }

    // Vérifier que le lot n'est pas déjà vendu
    if (lot.vendu) {
      throw new BadRequestError('Ce lot est déjà vendu');
    }

    // Mettre à jour le lot avec les informations d'enchère
    lot.dateDebutEnchere = dateDebutEnchere;
    lot.dateFinEnchere = dateFinEnchere;
    lot.isAuctionActive = true;
    lot.online = true; // Marquer l'enchère comme active

    await lot.save();

    // Planifier la fermeture automatique de l'enchère à la date de fin
    const timeUntilEnd = dateFinEnchere.getTime() - now.getTime();
    console.log(`[${new Date().toISOString()}] INFO [LOT] Enchère programmée pour se terminer dans ${timeUntilEnd / 1000} secondes`);

    // Stocker l'ID du lot et la date de fin dans une variable globale pour pouvoir annuler le timer si l'enchère est prolongée
    if (!global.auctionTimers) {
      global.auctionTimers = new Map();
    }

    // Annuler le timer existant si présent
    if (global.auctionTimers.has(lot._id.toString())) {
      clearTimeout(global.auctionTimers.get(lot._id.toString()).timerId);
      console.log(`[${new Date().toISOString()}] INFO [LOT] Timer existant annulé pour le lot ${lot._id}`);
    }

    // Créer un nouveau timer
    const timerId = setTimeout(async () => {
      try {
        // Récupérer le lot à nouveau pour avoir les données les plus récentes
        const lotToClose = await Lot.findById(lot._id);

        // Vérifier si l'enchère est toujours active
        if (lotToClose && lotToClose.isAuctionActive) {
          console.log(`[${new Date().toISOString()}] INFO [LOT] Fermeture automatique de l'enchère pour le lot ${lotToClose._id}`);

          // Marquer l'enchère comme inactive
          lotToClose.isAuctionActive = false;

          // Si une enchère a été placée, marquer le lot comme vendu
          if (lotToClose.acheteur && lotToClose.prixEnchere) {
            lotToClose.vendu = true;
            lotToClose.prixFinal = lotToClose.prixEnchere;
            lotToClose.dateSoumission = new Date();

            // Récupérer les informations complètes du lot
            const populatedLotToClose = await Lot.findById(lotToClose._id)
              .populate('veterinaire', 'nom prenom')
              .populate('user', 'nom prenom')
              .populate('acheteur', 'nom prenom');

            // Notifier l'acheteur
            if (populatedLotToClose.acheteur) {
              try {
                await notificationService.notifierClient(
                  populatedLotToClose.acheteur._id,
                  'Enchère remportée',
                  `Vous avez remporté l'enchère pour le lot ${populatedLotToClose.identifiant} de ${populatedLotToClose.especeNom || 'poisson'} pour ${lotToClose.prixFinal} dinars.`,
                  'success',
                  {
                    reference: lotToClose._id,
                    referenceModel: 'Lot',
                    urlAction: `/client/lots/${lotToClose._id}`
                  }
                );
              } catch (error) {
                console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification à l'acheteur:`, error);
              }
            }

            // Notifier le pêcheur
            if (populatedLotToClose.user) {
              try {
                await notificationService.notifierPecheur(
                  populatedLotToClose.user._id,
                  'Lot vendu',
                  `Votre lot ${populatedLotToClose.identifiant} de ${populatedLotToClose.especeNom || 'poisson'} a été vendu pour ${lotToClose.prixFinal} dinars.`,
                  'success',
                  {
                    reference: lotToClose._id,
                    referenceModel: 'Lot',
                    urlAction: `/pecheur/lots/${lotToClose._id}`
                  }
                );
              } catch (error) {
                console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification au pêcheur:`, error);
              }
            }

            // Notifier le mareyeur
            if (lotToClose.maryeur) {
              try {
                await notificationService.notifierMaryeur(
                  lotToClose.maryeur,
                  'Enchère terminée - Lot vendu',
                  `L'enchère pour le lot ${populatedLotToClose.identifiant} de ${populatedLotToClose.especeNom || 'poisson'} est terminée. Le lot a été vendu pour ${lotToClose.prixFinal} dinars.`,
                  'success',
                  {
                    reference: lotToClose._id,
                    referenceModel: 'Lot',
                    urlAction: `/maryeur/lots/${lotToClose._id}`
                  }
                );
              } catch (error) {
                console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification au mareyeur:`, error);
              }
            }
          } else {
            // Si aucune enchère n'a été placée, notifier le mareyeur
            if (lotToClose.maryeur) {
              try {
                await notificationService.notifierMaryeur(
                  lotToClose.maryeur,
                  'Enchère terminée - Aucune offre',
                  `L'enchère pour le lot ${lotToClose.identifiant} est terminée. Aucune offre n'a été placée.`,
                  'warning',
                  {
                    reference: lotToClose._id,
                    referenceModel: 'Lot',
                    urlAction: `/maryeur/lots/${lotToClose._id}`
                  }
                );
              } catch (error) {
                console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification au mareyeur:`, error);
              }
            }
          }

          // Sauvegarder les modifications
          await lotToClose.save();
          console.log(`[${new Date().toISOString()}] INFO [LOT] Enchère fermée automatiquement pour le lot ${lotToClose._id}`);

          // Notifier tous les clients que l'enchère est terminée
          try {
            const Client = require('../models/Client');
            const clients = await Client.find({ isValidated: true, isBlocked: false });

            for (const client of clients) {
              await notificationService.notifierClient(
                client._id,
                'Enchère terminée',
                `L'enchère pour le lot ${lotToClose.identifiant} est maintenant terminée.`,
                'info',
                {
                  reference: lotToClose._id,
                  referenceModel: 'Lot',
                  urlAction: `/client/auctions`
                }
              );
            }
          } catch (error) {
            console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi des notifications aux clients:`, error);
          }
        }
      } catch (error) {
        console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de la fermeture automatique de l'enchère:`, error);
      }

      // Supprimer le timer de la map
      if (global.auctionTimers && global.auctionTimers.has(lot._id.toString())) {
        global.auctionTimers.delete(lot._id.toString());
        console.log(`[${new Date().toISOString()}] INFO [LOT] Timer supprimé de la map pour le lot ${lot._id}`);
      }
    }, timeUntilEnd);

    // Stocker le timer et la date de fin dans la map
    global.auctionTimers.set(lot._id.toString(), {
      timerId: timerId,
      endDate: dateFinEnchere
    });
    console.log(`[${new Date().toISOString()}] INFO [LOT] Timer stocké dans la map pour le lot ${lot._id}`);

    // Récupérer le lot mis à jour avec les relations peuplées
    const populatedLot = await Lot.findById(lot._id)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    // Notifier les clients qu'une nouvelle enchère est disponible
    try {
      // Récupérer tous les clients
      const Client = require('../models/Client');
      const clients = await Client.find({ isValidated: true, isBlocked: false });

      // Envoyer une notification à chaque client
      for (const client of clients) {
        await notificationService.notifierClient(
          client._id,
          'Nouvelle enchère disponible',
          `Une nouvelle enchère pour ${populatedLot.especeNom || 'poisson'} est maintenant disponible. Prix initial: ${lot.prixInitial} dinars.`,
          'info',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/client/auctions/${lot._id}`
          }
        );
      }
    } catch (error) {
      console.error('Erreur lors de l\'envoi des notifications aux clients:', error);
    }

    res.success(populatedLot, 'Enchère démarrée avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/lots/:id/set-price
 * @desc Définir le prix initial et minimal d'un lot
 * @access Private (Mareyeur ou Admin)
 */
router.put('/:id/set-price', auth, checkRole(['ROLE_MARYEUR', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    // Vérifier que les données nécessaires sont présentes
    if (!req.body.prixMinimal) {
      throw new BadRequestError('Prix minimal requis');
    }

    // Convertir les prix en nombres
    const prixMinimal = parseFloat(req.body.prixMinimal);
    const prixInitial = parseFloat(req.body.prixInitial || req.body.prixMinimal);

    // Validation des prix
    if (isNaN(prixMinimal) || prixMinimal <= 0) {
      throw new BadRequestError('Prix minimal invalide - doit être un nombre positif');
    }

    if (isNaN(prixInitial) || prixInitial <= 0) {
      throw new BadRequestError('Prix initial invalide - doit être un nombre positif');
    }

    if (prixInitial < prixMinimal) {
      throw new BadRequestError('Le prix initial ne peut pas être inférieur au prix minimal');
    }

    // Mettre à jour le lot
    let lot = await Lot.findById(req.params.id);

    if (!lot) {
      // Si non trouvé par ID, essayer par identifiant
      lot = await Lot.findOne({ identifiant: req.params.id });
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    // Vérifier que le lot a été validé par un vétérinaire
    if (!lot.test || !lot.status) {
      throw new BadRequestError('Le lot doit être validé par un vétérinaire avant de définir un prix');
    }

    // Mettre à jour les prix
    lot.prixMinimal = prixMinimal;
    lot.prixInitial = prixInitial;
    lot.current = prixMinimal; // Prix courant initial = prix minimal

    // Ajouter d'autres champs si fournis
    if (req.body.typeEnchere) lot.typeEnchere = req.body.typeEnchere;
    if (req.body.online !== undefined) lot.online = req.body.online;

    await lot.save();

    // Récupérer le lot mis à jour avec les relations peuplées
    const populatedLot = await Lot.findById(lot._id)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    res.success(populatedLot, 'Prix du lot défini avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/lots/:id/auction/end
 * @desc Clôturer manuellement une enchère en cours
 * @access Private (Mareyeur ou Admin)
 */
router.put('/:id/auction/end', auth, checkRole(['ROLE_MARYEUR', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    // Récupérer le lot
    let lot;
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      lot = await Lot.findById(req.params.id);
    } else {
      lot = await Lot.findOne({ identifiant: req.params.id });
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    // Vérifier que le lot a un prix initial défini
    if (!lot.prixInitial) {
      throw new BadRequestError('Ce lot n\'a pas de prix initial défini');
    }

    // Vérifier que le lot n'est pas déjà vendu
    if (lot.vendu) {
      throw new BadRequestError('Ce lot est déjà vendu');
    }

    // Vérifier que l'enchère est active
    if (!lot.isAuctionActive) {
      throw new BadRequestError('Aucune enchère active pour ce lot');
    }

    // Marquer l'enchère comme inactive
    lot.isAuctionActive = false;

    // Annuler le timer de fermeture automatique
    if (global.auctionTimers && global.auctionTimers.has(lot._id.toString())) {
      clearTimeout(global.auctionTimers.get(lot._id.toString()).timerId);
      global.auctionTimers.delete(lot._id.toString());
      console.log(`[${new Date().toISOString()}] INFO [LOT] Timer annulé et supprimé pour le lot ${lot._id} (clôture manuelle)`);
    }

    // Déterminer l'acheteur et le prix final
    const acheteur = lot.acheteur;
    const prixFinal = lot.prixEnchere || lot.prixInitial;

    // Si une enchère a été placée, marquer le lot comme vendu
    if (acheteur && lot.prixEnchere) {
      lot.vendu = true;
      lot.prixFinal = prixFinal;
      lot.dateSoumission = new Date();
    }

    await lot.save();

    // Récupérer le lot mis à jour avec les relations peuplées
    const populatedLot = await Lot.findById(lot._id)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    // Notifier l'acheteur si disponible
    if (acheteur) {
      try {
        await notificationService.notifierClient(
          acheteur,
          'Enchère remportée',
          `Vous avez remporté l'enchère pour le lot ${populatedLot.identifiant} de ${populatedLot.espece ? populatedLot.espece.nom : 'poisson'} pour ${prixFinal} dinars.`,
          'success',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/client/lots/${lot._id}`
          }
        );
      } catch (error) {
        console.error('Erreur lors de l\'envoi de la notification au client:', error);
      }
    }

    // Notifier le pêcheur de la vente
    if (populatedLot.user) {
      try {
        await notificationService.notifierPecheur(
          populatedLot.user._id,
          'Lot vendu',
          `Votre lot ${populatedLot.identifiant} de ${populatedLot.especeNom || 'poisson'} a été vendu pour ${prixFinal} dinars.`,
          'success',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/pecheur/lots/${lot._id}`
          }
        );
      } catch (error) {
        console.error('Erreur lors de l\'envoi de la notification au pêcheur:', error);
      }
    }

    res.success(populatedLot, 'Enchère clôturée avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route PATCH /api/lots/:id/vente
 * @desc Marquer un lot comme vendu
 * @access Private (Mareyeur ou Admin)
 */
router.patch('/:id/vente', auth, checkRole(['ROLE_MARYEUR', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    // Vérifier que les données nécessaires sont présentes
    if (!req.body.acheteur || !req.body.prixFinal) {
      throw new BadRequestError('Acheteur et prix final sont requis');
    }

    // Vérifier que l'acheteur existe dans la collection Client
    let acheteur;
    if (mongoose.Types.ObjectId.isValid(req.body.acheteur)) {
      acheteur = await Client.findById(req.body.acheteur);
    }

    // Si non trouvé dans Client, essayer de trouver dans User (pour compatibilité)
    if (!acheteur) {
      const User = require('../models/User');
      acheteur = await User.findById(req.body.acheteur);
      if (!acheteur) {
        throw new NotFoundError('Acheteur non trouvé');
      }
    }

    // Mettre à jour les données de vente
    const updateData = {
      vendu: true,
      acheteur: req.body.acheteur,
      prixFinal: req.body.prixFinal,
      dateSoumission: new Date()
    };

    let lot;

    // Essayer de mettre à jour par ObjectId (MongoDB ID)
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      lot = await Lot.findByIdAndUpdate(req.params.id, updateData, {
        new: true,
        runValidators: true
      });
    }

    // Si non trouvé, essayer de mettre à jour par identifiant
    if (!lot) {
      lot = await Lot.findOneAndUpdate({ identifiant: req.params.id }, updateData, {
        new: true,
        runValidators: true
      });
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    // Récupérer le lot mis à jour avec les relations peuplées
    const populatedLot = await Lot.findById(lot._id)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    // Notifier le client de l'achat
    if (populatedLot.acheteur) {
      try {
        await notificationService.notifierClient(
          populatedLot.acheteur._id,
          'Achat confirmé',
          `Votre achat du lot ${populatedLot.identifiant} de ${populatedLot.espece ? populatedLot.espece.nom : 'poisson'} a été confirmé pour ${populatedLot.prixFinal} dinars.`,
          'success',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/client/achats/${lot._id}`
          }
        );
      } catch (error) {
        console.error('Erreur lors de l\'envoi de la notification au client:', error);
      }
    }

    // Notifier le pêcheur de la vente
    if (populatedLot.user) {
      try {
        await notificationService.notifierPecheur(
          populatedLot.user._id,
          'Lot vendu',
          `Votre lot ${populatedLot.identifiant} de ${populatedLot.especeNom || 'poisson'} a été vendu pour ${populatedLot.prixFinal} dinars.`,
          'success',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/pecheur/lots/${lot._id}`
          }
        );
      } catch (error) {
        console.error('Erreur lors de l\'envoi de la notification au pêcheur:', error);
      }
    }

    res.success(populatedLot, 'Lot marqué comme vendu avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/lots/cleanup-invalid
 * @desc Nettoyer les lots avec des données invalides
 * @access Private (Admin uniquement)
 */
router.delete('/cleanup-invalid', auth, checkRole(['ROLE_ADMIN']), async (req, res, next) => {
  try {
    console.log(`[${new Date().toISOString()}] INFO [LOT] Début du nettoyage des lots invalides`);

    // Critères pour identifier les lots invalides
    const invalidLotsFilter = {
      $or: [
        { especeNom: { $in: [null, '', 'Poisson'] } },
        { user: { $in: [null] } },
        { dateSoumission: { $in: [null] } },
        { photo: { $in: [null, ''] } },
        { quantite: { $lte: 0 } },
        { poids: { $lte: 0 } }
      ]
    };

    // Compter les lots invalides avant suppression
    const invalidCount = await Lot.countDocuments(invalidLotsFilter);
    console.log(`[${new Date().toISOString()}] INFO [LOT] Lots invalides trouvés: ${invalidCount}`);

    if (invalidCount === 0) {
      return res.success({ deletedCount: 0 }, 'Aucun lot invalide trouvé');
    }

    // Supprimer les lots invalides
    const result = await Lot.deleteMany(invalidLotsFilter);

    console.log(`[${new Date().toISOString()}] INFO [LOT] Lots invalides supprimés: ${result.deletedCount}`);

    res.success({
      deletedCount: result.deletedCount,
      message: `${result.deletedCount} lots invalides ont été supprimés`
    }, 'Nettoyage des lots invalides terminé avec succès');
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors du nettoyage:`, error);
    next(error);
  }
});

/**
 * @route GET /api/lots/auctions/active
 * @desc Récupérer toutes les enchères actives
 * @access Public
 */
router.get('/auctions/active', async (req, res, next) => {
  try {
    const now = new Date();

    // Récupérer tous les lots avec des enchères actives
    const lots = await Lot.find({
      isAuctionActive: true,
      dateFinEnchere: { $gt: now },
      vendu: false
    })
    .populate('acheteur', 'nom prenom')
    .populate('user', 'nom prenom')
    .populate('veterinaire', 'nom prenom')
    .sort({ dateFinEnchere: 1 }); // Trier par date de fin croissante

    // Standardiser la réponse pour être cohérent avec les autres routes
    res.success({ data: lots }, 'Enchères actives récupérées avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/lots/:id/auction/extend
 * @desc Prolonger la durée d'une enchère en cours
 * @access Private (Mareyeur ou Admin)
 */
router.put('/:id/auction/extend', auth, checkRole(['ROLE_MARYEUR', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    // Vérifier que les données nécessaires sont présentes
    if (!req.body.additionalMinutes || isNaN(parseInt(req.body.additionalMinutes)) || parseInt(req.body.additionalMinutes) <= 0) {
      throw new BadRequestError('Nombre de minutes supplémentaires requis (valeur positive)');
    }

    const additionalMinutes = parseInt(req.body.additionalMinutes);

    // Récupérer le lot
    let lot;
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      lot = await Lot.findById(req.params.id);
    } else {
      lot = await Lot.findOne({ identifiant: req.params.id });
    }

    if (!lot) {
      throw new NotFoundError('Lot non trouvé');
    }

    // Vérifier que l'enchère est active
    if (!lot.enchereActive) {
      throw new BadRequestError('Aucune enchère active pour ce lot');
    }

    // Vérifier que le lot n'est pas déjà vendu
    if (lot.vendu) {
      throw new BadRequestError('Ce lot est déjà vendu');
    }

    // Calculer la nouvelle date de fin
    const now = new Date();
    const currentEndDate = new Date(lot.dateFinEnchere);

    // Si la date de fin est déjà passée, partir de maintenant
    const baseDate = currentEndDate < now ? now : currentEndDate;
    const newEndDate = new Date(baseDate.getTime() + additionalMinutes * 60 * 1000);

    // Mettre à jour la date de fin de l'enchère
    lot.dateFinEnchere = newEndDate;
    await lot.save();

    // Mettre à jour le timer de fermeture automatique
    if (global.auctionTimers && global.auctionTimers.has(lot._id.toString())) {
      // Annuler le timer existant
      clearTimeout(global.auctionTimers.get(lot._id.toString()).timerId);

      // Calculer le nouveau délai
      const timeUntilEnd = newEndDate.getTime() - now.getTime();
      console.log(`[${new Date().toISOString()}] INFO [LOT] Enchère prolongée, nouveau délai: ${timeUntilEnd / 1000} secondes`);

      // Créer un nouveau timer
      const timerId = setTimeout(async () => {
        try {
          // Récupérer le lot à nouveau pour avoir les données les plus récentes
          const lotToClose = await Lot.findById(lot._id);

          // Vérifier si l'enchère est toujours active
          if (lotToClose && lotToClose.enchereActive) {
            console.log(`[${new Date().toISOString()}] INFO [LOT] Fermeture automatique de l'enchère prolongée pour le lot ${lotToClose._id}`);

            // Marquer l'enchère comme inactive
            lotToClose.enchereActive = false;

            // Si une enchère a été placée, marquer le lot comme vendu
            if (lotToClose.acheteur && lotToClose.prixEnchere) {
              lotToClose.vendu = true;
              lotToClose.prixFinal = lotToClose.prixEnchere;
              lotToClose.dateSoumission = new Date();

              // Récupérer les informations complètes du lot
              const populatedLotToClose = await Lot.findById(lotToClose._id)
                .populate('veterinaire', 'nom prenom')
                .populate('user', 'nom prenom')
                .populate('acheteur', 'nom prenom');

              // Notifier l'acheteur
              if (populatedLotToClose.acheteur) {
                try {
                  await notificationService.notifierClient(
                    populatedLotToClose.acheteur._id,
                    'Enchère remportée',
                    `Vous avez remporté l'enchère pour le lot ${populatedLotToClose.identifiant} de ${populatedLotToClose.especeNom || 'poisson'} pour ${lotToClose.prixFinal} dinars.`,
                    'success',
                    {
                      reference: lotToClose._id,
                      referenceModel: 'Lot',
                      urlAction: `/client/lots/${lotToClose._id}`
                    }
                  );
                } catch (error) {
                  console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification à l'acheteur:`, error);
                }
              }

              // Notifier le pêcheur
              if (populatedLotToClose.user) {
                try {
                  await notificationService.notifierPecheur(
                    populatedLotToClose.user._id,
                    'Lot vendu',
                    `Votre lot ${populatedLotToClose.identifiant} de ${populatedLotToClose.especeNom || 'poisson'} a été vendu pour ${lotToClose.prixFinal} dinars.`,
                    'success',
                    {
                      reference: lotToClose._id,
                      referenceModel: 'Lot',
                      urlAction: `/pecheur/lots/${lotToClose._id}`
                    }
                  );
                } catch (error) {
                  console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification au pêcheur:`, error);
                }
              }

              // Notifier le mareyeur
              if (lotToClose.maryeur) {
                try {
                  await notificationService.notifierMaryeur(
                    lotToClose.maryeur,
                    'Enchère terminée - Lot vendu',
                    `L'enchère pour le lot ${populatedLotToClose.identifiant} de ${populatedLotToClose.especeNom || 'poisson'} est terminée. Le lot a été vendu pour ${lotToClose.prixFinal} dinars.`,
                    'success',
                    {
                      reference: lotToClose._id,
                      referenceModel: 'Lot',
                      urlAction: `/maryeur/lots/${lotToClose._id}`
                    }
                  );
                } catch (error) {
                  console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification au mareyeur:`, error);
                }
              }
            } else {
              // Si aucune enchère n'a été placée, notifier le mareyeur
              if (lotToClose.maryeur) {
                try {
                  await notificationService.notifierMaryeur(
                    lotToClose.maryeur,
                    'Enchère terminée - Aucune offre',
                    `L'enchère pour le lot ${lotToClose.identifiant} est terminée. Aucune offre n'a été placée.`,
                    'warning',
                    {
                      reference: lotToClose._id,
                      referenceModel: 'Lot',
                      urlAction: `/maryeur/lots/${lotToClose._id}`
                    }
                  );
                } catch (error) {
                  console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi de la notification au mareyeur:`, error);
                }
              }
            }

            // Sauvegarder les modifications
            await lotToClose.save();
            console.log(`[${new Date().toISOString()}] INFO [LOT] Enchère fermée automatiquement pour le lot ${lotToClose._id}`);

            // Notifier tous les clients que l'enchère est terminée
            try {
              const Client = require('../models/Client');
              const clients = await Client.find({ isValidated: true, isBlocked: false });

              for (const client of clients) {
                await notificationService.notifierClient(
                  client._id,
                  'Enchère terminée',
                  `L'enchère pour le lot ${lotToClose.identifiant} est maintenant terminée.`,
                  'info',
                  {
                    reference: lotToClose._id,
                    referenceModel: 'Lot',
                    urlAction: `/client/auctions`
                  }
                );
              }
            } catch (error) {
              console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi des notifications aux clients:`, error);
            }
          }
        } catch (error) {
          console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de la fermeture automatique de l'enchère prolongée:`, error);
        }

        // Supprimer le timer de la map
        if (global.auctionTimers && global.auctionTimers.has(lot._id.toString())) {
          global.auctionTimers.delete(lot._id.toString());
          console.log(`[${new Date().toISOString()}] INFO [LOT] Timer supprimé de la map pour le lot ${lot._id}`);
        }
      }, timeUntilEnd);

      // Mettre à jour le timer dans la map
      global.auctionTimers.set(lot._id.toString(), {
        timerId: timerId,
        endDate: newEndDate
      });

      console.log(`[${new Date().toISOString()}] INFO [LOT] Timer mis à jour dans la map pour le lot ${lot._id}`);
    } else {
      console.log(`[${new Date().toISOString()}] WARN [LOT] Aucun timer trouvé pour le lot ${lot._id}, impossible de mettre à jour le timer`);
    }

    // Récupérer le lot mis à jour avec les relations peuplées
    const populatedLot = await Lot.findById(lot._id)
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    // Notifier tous les clients que l'enchère a été prolongée
    try {
      const Client = require('../models/Client');
      const clients = await Client.find({ isValidated: true, isBlocked: false });

      for (const client of clients) {
        await notificationService.notifierClient(
          client._id,
          'Enchère prolongée',
          `L'enchère pour le lot ${populatedLot.identifiant} de ${populatedLot.especeNom || 'poisson'} a été prolongée de ${additionalMinutes} minute(s). Nouvelle fin: ${newEndDate.toLocaleString()}.`,
          'info',
          {
            reference: lot._id,
            referenceModel: 'Lot',
            urlAction: `/client/auctions/${lot._id}`
          }
        );
      }

      // Envoyer une mise à jour en temps réel à tous les clients connectés
      const websocketService = require('../services/websocketService');
      websocketService.broadcastToAll('auction_extended', {
        lotId: lot._id,
        identifiant: lot.identifiant,
        dateFinEnchere: newEndDate,
        additionalMinutes: additionalMinutes
      });

    } catch (error) {
      console.error(`[${new Date().toISOString()}] ERROR [LOT] Erreur lors de l'envoi des notifications:`, error);
    }

    res.success(populatedLot, `Enchère prolongée de ${additionalMinutes} minute(s)`);
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/lots/sold/maryeur/:id
 * @desc Récupérer l'historique des lots vendus d'un mareyeur
 * @access Private (Maryeur ou Admin)
 */
router.get('/sold/maryeur/:id', auth, checkRole(['ROLE_MARYEUR', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    let filter = {};

    // Vérifier si l'ID est un ObjectId valide
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      filter.maryeur = req.params.id;
    } else {
      // Si ce n'est pas un ObjectId, chercher le maryeur par ID personnalisé
      const Maryeur = require('../models/Maryeur');
      const maryeur = await Maryeur.findOne({ id: req.params.id });

      if (!maryeur) {
        return res.success({ data: [] }, 'Aucun lot vendu trouvé pour ce maryeur');
      }

      filter.maryeur = maryeur._id;
    }

    // Filtrer les lots vendus uniquement
    filter.vendu = true;

    // Récupérer tous les lots vendus par ce maryeur
    const lots = await Lot.find(filter)
      .sort({ dateSoumission: -1 })
      .populate('veterinaire', 'nom prenom')
      .populate('user', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    res.success({ data: lots }, 'Historique des lots vendus récupéré avec succès');
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/lots/history/veterinaire/:id
 * @desc Récupérer l'historique complet des validations d'un vétérinaire
 * @access Private (Vétérinaire ou Admin)
 */
router.get('/history/veterinaire/:id', auth, checkRole(['ROLE_VETERINAIRE', 'ROLE_ADMIN']), async (req, res, next) => {
  try {
    let filter = {};

    // Vérifier si l'ID est un ObjectId valide
    if (mongoose.Types.ObjectId.isValid(req.params.id)) {
      filter.veterinaire = req.params.id;
    } else {
      // Si ce n'est pas un ObjectId, chercher le vétérinaire par ID personnalisé
      const Veterinaire = require('../models/Veterinaire');
      const veterinaire = await Veterinaire.findOne({ id: req.params.id });

      if (!veterinaire) {
        return res.success({ data: [] }, 'Aucun historique trouvé pour ce vétérinaire');
      }

      filter.veterinaire = veterinaire._id;
    }

    // Filtrer les lots qui ont été testés (validés ou refusés)
    filter.test = true;

    // Filtres optionnels
    if (req.query.status) {
      if (req.query.status === 'approved') {
        filter.status = true;
        filter.isValidated = true;
      } else if (req.query.status === 'rejected') {
        filter.status = false;
      }
    }

    // Récupérer l'historique des validations
    const lots = await Lot.find(filter)
      .sort({ dateTest: -1 })
      .populate('user', 'nom prenom')
      .populate('maryeur', 'nom prenom')
      .populate('acheteur', 'nom prenom');

    res.success({ data: lots }, 'Historique des validations récupéré avec succès');
  } catch (error) {
    next(error);
  }
});

module.exports = router;