const mongoose = require('mongoose');

// Schéma ultra-simplifié pour le lot qui englobe toutes les informations nécessaires
// Sans référence à Espece ou Prise
const lotSchema = new mongoose.Schema({
  // Identifiants - généré automatiquement
  identifiant: {
    type: String,
    unique: true,
    default: () => `LOT-${Date.now()}-${Math.floor(Math.random() * 1000)}`
  },

  // Informations de base du lot
  photo: String, // URL de l'image du poisson
  especeNom: {
    type: String,
    default: 'Poisson'
  }, // Nom de l'espèce directement (ex: "Rouget")
  // ✅ SUPPRIMÉ: quantite (le poids est suffisant)
  poids: {
    type: Number,
    default: 0
  },
  temperature: {
    type: Number,
    default: 0
  },
  lieu: {
    type: String,
    default: 'Non spécifié'
  },
  description: String,

  // Dates
  dateSoumission: {
    type: Date,
    default: Date.now
  },
  dateValidation: Date,
  dateEnchere: Date,
  dateFinEnchere: Date,
  dateDebutEnchere: Date,

  // Statuts
  isValidated: {
    type: Boolean,
    default: false
  },
  vendu: {
    type: Boolean,
    default: false
  },
  isAuctionActive: {
    type: Boolean,
    default: false
  },
  online: {
    type: Boolean,
    default: false
  },

  // Type d'enchère
  typeEnchere: {
    type: String,
    default: 'standard'
  },

  // Relations - uniquement les acteurs essentiels
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Pecheur'
  },
  veterinaire: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Veterinaire'
  },
  maryeur: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Maryeur'
  },
  acheteur: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Client'
  },

  // Prix
  prixInitial: Number,
  prixMinimal: Number,
  prixFinal: Number,
  prixActuel: Number,
  prixEnchere: Number,

  // Motif de refus (si le vétérinaire refuse le lot)
  motifRefus: String,
  raisonRejet: String, // Alias pour motifRefus pour compatibilité frontend

  // Statuts de test et validation (pour compatibilité)
  status: {
    type: Boolean,
    default: false
  },
  test: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    getters: true,
    transform: function(doc, ret) {
      // Ajouter id pour la compatibilité avec le frontend
      if (ret._id) {
        ret.id = ret._id.toString();
      }

      // Pour la compatibilité, ajouter imageUrl basé sur photo
      if (ret.photo && !ret.imageUrl) {
        ret.imageUrl = ret.photo;
      }

      // Assurer que les IDs sont des chaînes
      if (ret.user) ret.userId = ret.user.toString();
      if (ret.veterinaire) ret.veterinaireId = ret.veterinaire.toString();
      if (ret.maryeur) ret.maryeurId = ret.maryeur.toString();
      if (ret.acheteur) ret.acheteurId = ret.acheteur.toString();

      // Synchroniser les champs de rejet pour compatibilité
      if (ret.motifRefus && !ret.raisonRejet) {
        ret.raisonRejet = ret.motifRefus;
      }
      if (ret.raisonRejet && !ret.motifRefus) {
        ret.motifRefus = ret.raisonRejet;
      }

      return ret;
    }
  }
});

// Ajouter des index pour améliorer les performances des requêtes
lotSchema.index({ veterinaire: 1 });
lotSchema.index({ maryeur: 1 });
lotSchema.index({ user: 1 });
lotSchema.index({ isValidated: 1 });
lotSchema.index({ vendu: 1 });
lotSchema.index({ dateSoumission: -1 });
lotSchema.index({ isAuctionActive: 1 });

// Middleware pre-save pour validation
lotSchema.pre('save', function(next) {
  // Validation des champs requis
  if (!this.identifiant || !this.especeNom || !this.poids) {
    return next(new Error('Les champs identifiant, especeNom et poids sont requis'));
  }
  next();
});

const Lot = mongoose.model('Lot', lotSchema);

module.exports = Lot;