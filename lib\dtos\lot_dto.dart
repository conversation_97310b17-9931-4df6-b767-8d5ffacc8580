import 'package:seatrace/models/lot.dart';

/// DTO (Data Transfer Object) pour le lot
/// Version simplifiée sans référence à Espece ou Prise
class LotDto {
  final String id;
  final String identifiant;
  final String? photo;
  // ✅ SUPPRIMÉ: quantite (le poids est suffisant)
  final double poids;
  final String especeNom;
  final double temperature;
  final String dateTest;
  final bool test;
  final bool isValidated;
  final bool vendu;
  final String userId;
  final String dateSoumission;
  final double? prixInitial;
  final double? prixMinimal;
  final double? prixFinal;
  final String? acheteurId;
  final String? motifRefus;

  LotDto({
    required this.id,
    required this.identifiant,
    this.photo,
    // ✅ SUPPRIMÉ: quantite (le poids est suffisant)
    required this.poids,
    required this.especeNom,
    required this.temperature,
    required this.dateTest,
    required this.test,
    required this.isValidated,
    required this.vendu,
    required this.userId,
    required this.dateSoumission,
    this.prixInitial,
    this.prixMinimal,
    this.prixFinal,
    this.acheteurId,
    this.motifRefus,
  });

  /// Crée un LotDto à partir d'un Map (JSON)
  factory LotDto.fromJson(Map<String, dynamic> json) {
    return LotDto(
      id: json['_id'] ?? json['id'] ?? '',
      identifiant: json['identifiant'] ?? '',
      photo: json['photo'],
      // ✅ SUPPRIMÉ: quantite (le poids est suffisant)
      poids: _parseDouble(json['poids']) ?? 0.0,
      especeNom:
          json['especeNom'] ??
          json['espece_nom'] ??
          json['espece']?.toString() ??
          'Poisson',
      temperature: _parseDouble(json['temperature']) ?? 0.0,
      dateTest: json['dateTest'] ?? '',
      test: _parseBool(json['test']),
      isValidated: _parseBool(
        json['status'] ??
            json['isValid'] ??
            json['is_valid'] ??
            json['isValidated'],
      ),
      vendu: _parseBool(json['vendu']),
      userId: json['user'] ?? json['userId'] ?? '',
      dateSoumission: json['dateSoumission'] ?? '',
      prixInitial: _parseDouble(json['prixInitial'] ?? json['prixinitial']),
      prixMinimal: _parseDouble(json['prixMinimal'] ?? json['prixminimal']),
      prixFinal: _parseDouble(json['prixFinal'] ?? json['prixfinale']),
      acheteurId: json['acheteur'] ?? json['acheteurId'],
      motifRefus: json['motifRefus'] ?? json['motif_refus'],
    );
  }

  /// Convertit le DTO en modèle Lot
  Lot toLot() {
    return Lot(
      id: id,
      identifiant: identifiant,
      photo: photo,
      quantite: quantite,
      poids: poids,
      especeNom: especeNom,
      temperature: temperature,
      dateTest: _parseDateTime(dateTest),
      test: test,
      isValidated: isValidated,
      vendu: vendu,
      userId: userId,
      dateSoumission: _parseDateTime(dateSoumission),
      prixInitial: prixInitial,
      prixMinimal: prixMinimal,
      prixFinal: prixFinal,
      acheteurId: acheteurId,
      motifRefus: motifRefus,
    );
  }

  /// Convertit le DTO en Map (JSON)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'identifiant': identifiant,
      'photo': photo,
      'quantite': quantite,
      'poids': poids,
      'especeNom': especeNom,
      'temperature': temperature,
      'dateTest': dateTest,
      'test': test,
      'isValidated': isValidated, // Utiliser isValidated de manière cohérente
      'vendu': vendu,
      'user': userId,
      'dateSoumission': dateSoumission,
      'prixInitial': prixInitial,
      'prixMinimal': prixMinimal,
      'prixFinal': prixFinal,
      'acheteur': acheteurId,
      'motifRefus': motifRefus,
    };
  }

  /// Crée une copie du DTO avec des valeurs modifiées
  LotDto copyWith({
    String? id,
    String? identifiant,
    String? photo,
    int? quantite,
    double? poids,
    String? especeNom,
    double? temperature,
    String? dateTest,
    bool? test,
    bool? isValidated,
    bool? vendu,
    String? userId,
    String? dateSoumission,
    double? prixInitial,
    double? prixMinimal,
    double? prixFinal,
    String? acheteurId,
    String? motifRefus,
  }) {
    return LotDto(
      id: id ?? this.id,
      identifiant: identifiant ?? this.identifiant,
      photo: photo ?? this.photo,
      quantite: quantite ?? this.quantite,
      poids: poids ?? this.poids,
      especeNom: especeNom ?? this.especeNom,
      temperature: temperature ?? this.temperature,
      dateTest: dateTest ?? this.dateTest,
      test: test ?? this.test,
      isValidated: isValidated ?? this.isValidated,
      vendu: vendu ?? this.vendu,
      userId: userId ?? this.userId,
      dateSoumission: dateSoumission ?? this.dateSoumission,
      prixInitial: prixInitial ?? this.prixInitial,
      prixMinimal: prixMinimal ?? this.prixMinimal,
      prixFinal: prixFinal ?? this.prixFinal,
      acheteurId: acheteurId ?? this.acheteurId,
      motifRefus: motifRefus ?? this.motifRefus,
    );
  }

  /// Convertit une valeur en double
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (_) {
        return null;
      }
    }
    return null;
  }

  /// Convertit une valeur en int
  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      try {
        return int.parse(value);
      } catch (_) {
        return null;
      }
    }
    return null;
  }

  /// Convertit une valeur en bool
  static bool _parseBool(dynamic value) {
    if (value == null) return false;
    if (value is bool) return value;
    if (value is int) return value == 1;
    if (value is String) {
      return value.toLowerCase() == 'true' || value == '1';
    }
    return false;
  }

  /// Convertit une valeur en DateTime
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is DateTime) return value;
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (_) {
        // Si le format standard échoue, essayer d'autres formats
        try {
          // Format DD/MM/YYYY
          final parts = value.split('/');
          if (parts.length == 3) {
            return DateTime(
              int.parse(parts[2]), // année
              int.parse(parts[1]), // mois
              int.parse(parts[0]), // jour
            );
          }
        } catch (_) {}
        return null;
      }
    }
    return null;
  }
}
