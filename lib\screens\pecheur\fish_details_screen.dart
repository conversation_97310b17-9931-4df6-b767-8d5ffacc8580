import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/utils/animation_service.dart';
import 'package:seatrace/utils/responsive_service.dart';
import 'package:seatrace/widgets/sea_widgets.dart';
import 'package:intl/intl.dart';

class FishDetailsScreen extends StatefulWidget {
  final File imageFile;
  final String especeNom;

  const FishDetailsScreen({
    super.key,
    required this.imageFile,
    required this.especeNom,
  });

  @override
  State<FishDetailsScreen> createState() => _FishDetailsScreenState();
}

class _FishDetailsScreenState extends State<FishDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  // Nous avons supprimé le champ de quantité pour ne garder que le poids
  final _poidController = TextEditingController();
  final _temperatureController = TextEditingController();
  final _enginController = TextEditingController();
  final _zoneController = TextEditingController();

  final _emplacementController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  // Liste des mareyeurs disponibles
  List<Map<String, dynamic>> _maryeurs = [];
  // Mareyeur sélectionné
  Map<String, dynamic>? _selectedMaryeur;

  // Liste des vétérinaires disponibles
  List<Map<String, dynamic>> _veterinaires = [];
  // Vétérinaire sélectionné
  Map<String, dynamic>? _selectedVeterinaire;

  final _animationService = AnimationService();
  final _responsiveService = ResponsiveService();

  final List<String> _methodesDepeche = [
    'Filet',
    'Ligne',
    'Chalut',
    'Casier',
    'Palangre',
    'Autre',
  ];

  final List<String> _zonesDepeche = [
    'Méditerranée Nord',
    'Méditerranée Sud',
    'Atlantique Nord',
    'Atlantique Sud',
    'Manche',
    'Mer du Nord',
    'Autre',
  ];

  @override
  void initState() {
    super.initState();
    _enginController.text = 'Filet'; // Default value
    _zoneController.text = 'Méditerranée Nord'; // Default value
    _temperatureController.text = '4'; // Default value

    // Vérifier la configuration de l'API
    final apiService = UnifiedApiService();
    debugPrint('URL de base de l\'API: ${apiService.getBaseUrl()}');

    // Test direct pour vérifier si les vétérinaires et les maryeurs existent
    _testDirectApiAccess();

    // Charger la liste des mareyeurs et des vétérinaires
    _loadMaryeurs();
    _loadVeterinaires();
  }

  // Charger la liste des mareyeurs disponibles
  Future<void> _loadMaryeurs() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      debugPrint('Chargement des mareyeurs...');

      // Vérifier si l'utilisateur est connecté
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      debugPrint('Utilisateur connecté: ${user.prenom} ${user.nom}');

      // Vérifier la connectivité au serveur
      final apiService = UnifiedApiService();
      final isConnected = await apiService.checkServerConnectivity();
      if (!isConnected) {
        throw Exception('Impossible de se connecter au serveur');
      }

      debugPrint('Connectivité au serveur vérifiée: OK');
      debugPrint('URL de base de l\'API: ${apiService.getBaseUrl()}');

      // Récupérer les mareyeurs via le service unifié
      debugPrint('Appel de getAllMaryeurs()...');
      final maryeursData = await apiService.getAllMaryeurs();
      debugPrint('Nombre de mareyeurs récupérés: ${maryeursData.length}');

      // Vérification supplémentaire - si aucun mareyeur n'est récupéré, essayer une approche directe
      if (maryeursData.isEmpty) {
        debugPrint(
          'AUCUN MAREYEUR RÉCUPÉRÉ VIA getAllMaryeurs() - TENTATIVE DIRECTE',
        );

        // Récupérer directement les mareyeurs sans filtre
        final response = await apiService.get('maryeurs');

        // Extraire les données
        List<dynamic> rawData = [];
        if (response.containsKey('data')) {
          if (response['data'] is List) {
            rawData = response['data'] as List<dynamic>;
          } else if (response['data'] is Map &&
              (response['data'] as Map).containsKey('data')) {
            rawData = (response['data'] as Map)['data'] as List<dynamic>;
          }
        }

        debugPrint(
          'Nombre de mareyeurs récupérés directement: ${rawData.length}',
        );

        // Si des mareyeurs sont trouvés, les convertir en objets Maryeur
        if (rawData.isNotEmpty) {
          debugPrint('CONVERSION MANUELLE DES MAREYEURS BRUTS');

          // Filtrer les mareyeurs validés et non bloqués
          final filteredData =
              rawData
                  .where(
                    (item) =>
                        item is Map &&
                        (item['isValidated'] == true ||
                            item['isValid'] == true) &&
                        (item['isBlocked'] == false ||
                            item['isBlocked'] == null),
                  )
                  .toList();

          debugPrint('Nombre de mareyeurs filtrés: ${filteredData.length}');

          // Créer directement des Maps pour l'interface utilisateur
          for (final item in filteredData) {
            if (item is Map) {
              final id = item['_id']?.toString() ?? item['id']?.toString();
              final nom = item['nom']?.toString() ?? '';
              final prenom = item['prenom']?.toString() ?? '';

              if (id != null &&
                  id.isNotEmpty &&
                  nom.isNotEmpty &&
                  prenom.isNotEmpty) {
                final maryeurMap = {
                  '_id': id,
                  'nom': nom,
                  'prenom': prenom,
                  'email': item['email']?.toString() ?? '',
                  'telephone': item['telephone']?.toString() ?? '',
                  'port': item['port']?.toString() ?? '',
                  'matricule': item['matricule']?.toString() ?? '',
                  'isValidated':
                      item['isValidated'] == true || item['isValid'] == true,
                };

                // Ajouter directement à la liste des mareyeurs
                _maryeurs.add(maryeurMap);

                debugPrint(
                  'Mareyeur ajouté manuellement: ${maryeurMap['prenom']} ${maryeurMap['nom']}',
                );
              }
            }
          }

          // Si des mareyeurs ont été ajoutés, sélectionner le premier
          if (_maryeurs.isNotEmpty) {
            _selectedMaryeur = _maryeurs[0];
            debugPrint(
              'Mareyeur sélectionné manuellement: ${_selectedMaryeur!['prenom']} ${_selectedMaryeur!['nom']}',
            );

            // Mettre à jour l'interface
            setState(() {
              _isLoading = false;
              _errorMessage = null;
            });

            // Sortir de la méthode
            return;
          }
        }
      }

      // Afficher les détails de chaque mareyeur pour le débogage
      if (maryeursData.isNotEmpty) {
        debugPrint('=== DÉTAILS DES MAREYEURS RÉCUPÉRÉS ===');
        for (var i = 0; i < maryeursData.length; i++) {
          final mar = maryeursData[i];
          debugPrint(
            'Mareyeur #$i: ID=${mar.id}, Nom=${mar.prenom} ${mar.nom}, '
            'Email=${mar.email}, Validé=${mar.isValidated}, Bloqué=${mar.isBlocked}',
          );
        }
      } else {
        debugPrint('AUCUN MAREYEUR RÉCUPÉRÉ');

        // Essayer une requête directe pour diagnostiquer
        debugPrint('Tentative de requête directe à l\'API...');
        try {
          final response = await apiService.get(
            'maryeurs',
            queryParameters: {'isValidated': 'true', 'isBlocked': 'false'},
          );
          debugPrint('Réponse brute de l\'API: $response');
        } catch (directError) {
          debugPrint('Erreur lors de la requête directe: $directError');
        }
      }

      // Convertir les objets Maryeur en Map pour la compatibilité avec le code existant
      final maryeurs = <Map<String, dynamic>>[];

      for (final mar in maryeursData) {
        final mongoId = mar.id;
        final nom = mar.nom;
        final prenom = mar.prenom;
        final isValidated = mar.isValidated;

        // Vérifier que les données sont valides
        if (mongoId == null || mongoId.isEmpty) {
          debugPrint(
            '⚠️ AVERTISSEMENT: Mareyeur sans ID valide trouvé, ignoré',
          );
          continue;
        }

        if (nom.isEmpty || prenom.isEmpty) {
          debugPrint(
            '⚠️ AVERTISSEMENT: Mareyeur sans nom/prénom valide trouvé, ignoré',
          );
          continue;
        }

        debugPrint(
          '✅ Mareyeur valide trouvé: $prenom $nom (ID: $mongoId, Validé: $isValidated)',
        );

        final maryeurMap = {
          '_id': mongoId,
          'nom': nom,
          'prenom': prenom,
          'email': mar.email,
          'telephone': mar.telephone ?? '',
          'port': mar.port ?? '',
          'matricule': mar.matricule ?? '',
          'isValidated': isValidated,
        };

        maryeurs.add(maryeurMap);
      }

      debugPrint(
        'Nombre de mareyeurs convertis pour l\'UI: ${maryeurs.length}',
      );

      // Message d'erreur si aucun mareyeur n'est trouvé
      String? errorMsg;
      if (maryeurs.isEmpty) {
        errorMsg =
            'Aucun mareyeur validé disponible dans la base de données. '
            'Veuillez contacter l\'administrateur pour ajouter des mareyeurs.';
        debugPrint('Message d\'erreur: aucun mareyeur disponible');

        // Exécuter le test direct pour diagnostiquer le problème
        await _testDirectApiAccess();
      }

      if (mounted) {
        setState(() {
          // Utiliser directement la liste des mareyeurs
          _maryeurs = maryeurs;
          _isLoading = false;

          // Sélectionner le premier mareyeur par défaut s'il y en a
          if (_maryeurs.isNotEmpty) {
            _selectedMaryeur = _maryeurs[0];
            debugPrint(
              'Mareyeur sélectionné par défaut: ${_selectedMaryeur!['prenom']} ${_selectedMaryeur!['nom']}',
            );
          } else {
            // Si aucun mareyeur n'est disponible, afficher un message d'erreur
            _errorMessage = errorMsg;
          }
        });
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des mareyeurs: $e');

      // Exécuter le test direct pour diagnostiquer le problème
      await _testDirectApiAccess();

      if (mounted) {
        setState(() {
          _isLoading = false;
          _maryeurs = [];
          _selectedMaryeur = null;

          // Message d'erreur plus convivial
          if (e.toString().contains('network') ||
              e.toString().contains('connexion') ||
              e.toString().contains('SocketException') ||
              e.toString().contains('timeout')) {
            _errorMessage =
                'Problème de connexion au serveur. Vérifiez que le serveur backend est en cours d\'exécution et que votre connexion internet fonctionne.';
          } else {
            _errorMessage =
                'Erreur lors du chargement des mareyeurs: ${e.toString()}. Veuillez réessayer ou contacter l\'administrateur.';
          }
        });
      }
    }
  }

  // Charger la liste des vétérinaires disponibles
  Future<void> _loadVeterinaires() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      debugPrint('Chargement des vétérinaires...');

      // Vérifier si l'utilisateur est connecté
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      debugPrint('Utilisateur connecté: ${user.prenom} ${user.nom}');

      // Vérifier la connectivité au serveur
      final apiService = UnifiedApiService();
      final isConnected = await apiService.checkServerConnectivity();
      if (!isConnected) {
        throw Exception('Impossible de se connecter au serveur');
      }

      debugPrint('Connectivité au serveur vérifiée: OK');
      debugPrint('URL de base de l\'API: ${apiService.getBaseUrl()}');

      // Récupérer les vétérinaires via le service unifié
      debugPrint('Appel de getAllVeterinaires()...');
      final veterinairesData = await apiService.getAllVeterinaires();
      debugPrint(
        'Nombre de vétérinaires récupérés: ${veterinairesData.length}',
      );

      // Vérification supplémentaire - si aucun vétérinaire n'est récupéré, essayer une approche directe
      if (veterinairesData.isEmpty) {
        debugPrint(
          'AUCUN VÉTÉRINAIRE RÉCUPÉRÉ VIA getAllVeterinaires() - TENTATIVE DIRECTE',
        );

        // Récupérer directement les vétérinaires sans filtre
        final response = await apiService.get('veterinaires');

        // Extraire les données
        List<dynamic> rawData = [];
        if (response.containsKey('data')) {
          if (response['data'] is List) {
            rawData = response['data'] as List<dynamic>;
          } else if (response['data'] is Map &&
              (response['data'] as Map).containsKey('data')) {
            rawData = (response['data'] as Map)['data'] as List<dynamic>;
          }
        }

        debugPrint(
          'Nombre de vétérinaires récupérés directement: ${rawData.length}',
        );

        // Si des vétérinaires sont trouvés, les convertir en objets Veterinaire
        if (rawData.isNotEmpty) {
          debugPrint('CONVERSION MANUELLE DES VÉTÉRINAIRES BRUTS');

          // Filtrer les vétérinaires validés et non bloqués
          final filteredData =
              rawData
                  .where(
                    (item) =>
                        item is Map &&
                        (item['isValidated'] == true ||
                            item['isValid'] == true) &&
                        (item['isBlocked'] == false ||
                            item['isBlocked'] == null),
                  )
                  .toList();

          debugPrint('Nombre de vétérinaires filtrés: ${filteredData.length}');

          // Créer directement des Maps pour l'interface utilisateur
          for (final item in filteredData) {
            if (item is Map) {
              final id = item['_id']?.toString() ?? item['id']?.toString();
              final nom = item['nom']?.toString() ?? '';
              final prenom = item['prenom']?.toString() ?? '';

              if (id != null &&
                  id.isNotEmpty &&
                  nom.isNotEmpty &&
                  prenom.isNotEmpty) {
                final veterinaireMap = {
                  '_id': id,
                  'nom': nom,
                  'prenom': prenom,
                  'email': item['email']?.toString() ?? '',
                  'telephone': item['telephone']?.toString() ?? '',
                  'port': item['port']?.toString() ?? '',
                  'matricule': item['matricule']?.toString() ?? '',
                  'isValidated':
                      item['isValidated'] == true || item['isValid'] == true,
                };

                // Ajouter directement à la liste des vétérinaires
                _veterinaires.add(veterinaireMap);

                debugPrint(
                  'Vétérinaire ajouté manuellement: ${veterinaireMap['prenom']} ${veterinaireMap['nom']}',
                );
              }
            }
          }

          // Si des vétérinaires ont été ajoutés, sélectionner le premier
          if (_veterinaires.isNotEmpty) {
            _selectedVeterinaire = _veterinaires[0];
            debugPrint(
              'Vétérinaire sélectionné manuellement: ${_selectedVeterinaire!['prenom']} ${_selectedVeterinaire!['nom']}',
            );

            // Mettre à jour l'interface
            setState(() {
              _isLoading = false;
              _errorMessage = null;
            });

            // Sortir de la méthode
            return;
          }
        }
      }

      // Afficher les détails de chaque vétérinaire pour le débogage
      if (veterinairesData.isNotEmpty) {
        debugPrint('=== DÉTAILS DES VÉTÉRINAIRES RÉCUPÉRÉS ===');
        for (var i = 0; i < veterinairesData.length; i++) {
          final vet = veterinairesData[i];
          debugPrint(
            'Vétérinaire #$i: ID=${vet.id}, Nom=${vet.prenom} ${vet.nom}, '
            'Email=${vet.email}, Validé=${vet.isValidated}, Bloqué=${vet.isBlocked}',
          );
        }
      } else {
        debugPrint('AUCUN VÉTÉRINAIRE RÉCUPÉRÉ');

        // Essayer une requête directe pour diagnostiquer
        debugPrint('Tentative de requête directe à l\'API...');
        try {
          final response = await apiService.get(
            'veterinaires',
            queryParameters: {'isValidated': 'true', 'isBlocked': 'false'},
          );
          debugPrint('Réponse brute de l\'API: $response');
        } catch (directError) {
          debugPrint('Erreur lors de la requête directe: $directError');
        }
      }

      // Convertir les objets Veterinaire en Map pour la compatibilité avec le code existant
      final veterinaires = <Map<String, dynamic>>[];

      for (final vet in veterinairesData) {
        final mongoId = vet.id;
        final nom = vet.nom;
        final prenom = vet.prenom;
        final isValidated = vet.isValidated;

        // Vérifier que les données sont valides
        if (mongoId == null || mongoId.isEmpty) {
          debugPrint(
            '⚠️ AVERTISSEMENT: Vétérinaire sans ID valide trouvé, ignoré',
          );
          continue;
        }

        if (nom.isEmpty || prenom.isEmpty) {
          debugPrint(
            '⚠️ AVERTISSEMENT: Vétérinaire sans nom/prénom valide trouvé, ignoré',
          );
          continue;
        }

        debugPrint(
          '✅ Vétérinaire valide trouvé: $prenom $nom (ID: $mongoId, Validé: $isValidated)',
        );

        final veterinaireMap = {
          '_id': mongoId,
          'nom': nom,
          'prenom': prenom,
          'email': vet.email,
          'telephone': vet.telephone ?? '',
          'port': vet.port ?? '',
          'matricule': vet.matricule ?? '',
          'isValidated': isValidated,
        };

        veterinaires.add(veterinaireMap);
      }

      debugPrint(
        'Nombre de vétérinaires convertis pour l\'UI: ${veterinaires.length}',
      );

      // Message d'erreur si aucun vétérinaire n'est trouvé
      String? errorMsg;
      if (veterinaires.isEmpty) {
        errorMsg =
            'Aucun vétérinaire validé disponible dans la base de données. '
            'Veuillez contacter l\'administrateur pour ajouter des vétérinaires.';
        debugPrint('Message d\'erreur: aucun vétérinaire disponible');

        // Exécuter le test direct pour diagnostiquer le problème
        await _testDirectApiAccess();
      }

      if (mounted) {
        setState(() {
          // Utiliser directement la liste des vétérinaires
          _veterinaires = veterinaires;
          _isLoading = false;

          // Sélectionner le premier vétérinaire par défaut s'il y en a
          if (_veterinaires.isNotEmpty) {
            _selectedVeterinaire = _veterinaires[0];
            debugPrint(
              'Vétérinaire sélectionné par défaut: ${_selectedVeterinaire!['prenom']} ${_selectedVeterinaire!['nom']}',
            );
          } else {
            // Si aucun vétérinaire n'est disponible, afficher un message d'erreur
            _errorMessage = errorMsg;
          }
        });
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des vétérinaires: $e');

      // Exécuter le test direct pour diagnostiquer le problème
      await _testDirectApiAccess();

      if (mounted) {
        setState(() {
          _isLoading = false;
          _veterinaires = [];
          _selectedVeterinaire = null;

          // Message d'erreur plus convivial
          if (e.toString().contains('network') ||
              e.toString().contains('connexion') ||
              e.toString().contains('SocketException') ||
              e.toString().contains('timeout')) {
            _errorMessage =
                'Problème de connexion au serveur. Vérifiez que le serveur backend est en cours d\'exécution et que votre connexion internet fonctionne.';
          } else {
            _errorMessage =
                'Erreur lors du chargement des vétérinaires: ${e.toString()}. Veuillez réessayer ou contacter l\'administrateur.';
          }
        });
      }
    }
  }

  // Test direct pour vérifier si les vétérinaires et les maryeurs existent dans la base de données
  Future<void> _testDirectApiAccess() async {
    try {
      debugPrint('\n=== DÉBUT DES TESTS DIRECTS API ===');
      final apiService = UnifiedApiService();

      // Vérifier la connectivité au serveur
      final isConnected = await apiService.checkServerConnectivity();
      if (!isConnected) {
        debugPrint('ERREUR: Impossible de se connecter au serveur');
        return;
      }

      debugPrint('Connectivité au serveur vérifiée: OK');
      debugPrint('URL de base de l\'API: ${apiService.getBaseUrl()}');

      // Test 1: Récupérer tous les mareyeurs sans filtre
      debugPrint('\nTest 1: Récupération de tous les mareyeurs sans filtre');
      try {
        final response = await apiService.get('maryeurs');
        debugPrint('Réponse brute: $response');

        if (response.containsKey('data')) {
          final data = response['data'];
          if (data is List) {
            debugPrint('Nombre total de mareyeurs: ${data.length}');
            for (var i = 0; i < data.length; i++) {
              final maryeur = data[i];
              debugPrint(
                'Mareyeur #$i: ${maryeur['prenom']} ${maryeur['nom']}, '
                'Validé: ${maryeur['isValidated']}, Bloqué: ${maryeur['isBlocked']}',
              );
            }
          }
        }
      } catch (e) {
        debugPrint('Erreur lors du test 1: $e');
      }

      // Test 2: Récupérer tous les vétérinaires sans filtre
      debugPrint('\nTest 2: Récupération de tous les vétérinaires sans filtre');
      try {
        final response = await apiService.get('veterinaires');
        debugPrint('Réponse brute: $response');

        if (response.containsKey('data')) {
          final data = response['data'];
          if (data is List) {
            debugPrint('Nombre total de vétérinaires: ${data.length}');
            for (var i = 0; i < data.length; i++) {
              final veterinaire = data[i];
              debugPrint(
                'Vétérinaire #$i: ${veterinaire['prenom']} ${veterinaire['nom']}, '
                'Validé: ${veterinaire['isValidated']}, Bloqué: ${veterinaire['isBlocked']}',
              );
            }
          }
        }
      } catch (e) {
        debugPrint('Erreur lors du test 2: $e');
      }

      // Test 3: Récupérer les mareyeurs avec filtre explicite
      debugPrint(
        '\nTest 3: Récupération des mareyeurs avec filtre isValidated=true et isBlocked=false',
      );

      try {
        final response = await apiService.get(
          'maryeurs',
          queryParameters: {'isValidated': 'true', 'isBlocked': 'false'},
        );
        debugPrint('Réponse brute: $response');

        if (response.containsKey('data')) {
          final data = response['data'];
          if (data is List) {
            debugPrint(
              'Nombre de mareyeurs validés et non bloqués: ${data.length}',
            );
            for (var i = 0; i < data.length; i++) {
              final maryeur = data[i];
              debugPrint(
                'Mareyeur #$i: ${maryeur['prenom']} ${maryeur['nom']}, '
                'Validé: ${maryeur['isValidated']}, Bloqué: ${maryeur['isBlocked']}',
              );
            }
          }
        }
      } catch (e) {
        debugPrint('Erreur lors du test 3: $e');
      }

      // Test 4: Récupérer les vétérinaires avec filtre explicite
      debugPrint(
        '\nTest 4: Récupération des vétérinaires avec filtre isValidated=true et isBlocked=false',
      );

      try {
        final response = await apiService.get(
          'veterinaires',
          queryParameters: {'isValidated': 'true', 'isBlocked': 'false'},
        );
        debugPrint('Réponse brute: $response');

        if (response.containsKey('data')) {
          final data = response['data'];
          if (data is List) {
            debugPrint(
              'Nombre de vétérinaires validés et non bloqués: ${data.length}',
            );
            for (var i = 0; i < data.length; i++) {
              final veterinaire = data[i];
              debugPrint(
                'Vétérinaire #$i: ${veterinaire['prenom']} ${veterinaire['nom']}, '
                'Validé: ${veterinaire['isValidated']}, Bloqué: ${veterinaire['isBlocked']}',
              );
            }
          }
        }
      } catch (e) {
        debugPrint('Erreur lors du test 4: $e');
      }

      // Essayer d'abord avec le paramètre explicite isValidated=true
      debugPrint('\nTentative avec paramètre isValidated=true...');
      try {
        final vetResponseWithParam = await apiService.get(
          'veterinaires',
          queryParameters: {'isValidated': 'true'},
        );

        // Afficher la structure complète de la réponse
        debugPrint(
          'Structure de la réponse: ${vetResponseWithParam.keys.join(', ')}',
        );

        // Essayer d'extraire les données avec différentes structures
        List<dynamic> vetsWithParam = [];

        // Structure 1: {"success":true,"message":"...","data":{"data":[...]}}
        if (vetResponseWithParam.containsKey('data') &&
            vetResponseWithParam['data'] is Map &&
            (vetResponseWithParam['data'] as Map).containsKey('data') &&
            (vetResponseWithParam['data'] as Map)['data'] is List) {
          vetsWithParam =
              (vetResponseWithParam['data'] as Map)['data'] as List<dynamic>;
          debugPrint('Données extraites via structure data.data');
        }
        // Structure 2: {"success":true,"message":"...","data":[...]}
        else if (vetResponseWithParam.containsKey('data') &&
            vetResponseWithParam['data'] is List) {
          vetsWithParam = vetResponseWithParam['data'] as List<dynamic>;
          debugPrint('Données extraites via structure data (liste)');
        }
        // Structure 3: Recherche directe dans la réponse
        else {
          for (final key in vetResponseWithParam.keys) {
            if (vetResponseWithParam[key] is List) {
              vetsWithParam = vetResponseWithParam[key] as List<dynamic>;
              debugPrint('Données extraites via structure $key');
              break;
            }
          }
        }

        debugPrint(
          'Nombre de vétérinaires validés trouvés (avec paramètre): ${vetsWithParam.length}',
        );

        if (vetsWithParam.isNotEmpty) {
          // Afficher tous les vétérinaires validés pour le débogage
          for (var i = 0; i < vetsWithParam.length; i++) {
            final vet = vetsWithParam[i];
            debugPrint(
              'Vétérinaire #$i: '
              'ID: ${vet['_id'] ?? vet['id']}, '
              'Nom: ${vet['prenom']} ${vet['nom']}, '
              'Validé: ${vet['isValidated']}, '
              'Bloqué: ${vet['isBlocked']}',
            );
          }
        } else {
          debugPrint(
            'Aucun vétérinaire validé trouvé avec le paramètre isValidated=true',
          );
        }
      } catch (e) {
        debugPrint(
          'Erreur lors de la récupération avec paramètre isValidated=true: $e',
        );
      }

      // Maintenant essayer sans paramètre (comportement par défaut)
      debugPrint('\nTentative sans paramètre...');
      try {
        final vetResponse = await apiService.get('veterinaires');

        // Afficher la structure complète de la réponse
        debugPrint('Structure de la réponse: ${vetResponse.keys.join(', ')}');

        // Essayer d'extraire les données avec différentes structures
        List<dynamic> vets = [];

        // Structure 1: {"success":true,"message":"...","data":{"data":[...]}}
        if (vetResponse.containsKey('data') &&
            vetResponse['data'] is Map &&
            (vetResponse['data'] as Map).containsKey('data') &&
            (vetResponse['data'] as Map)['data'] is List) {
          vets = (vetResponse['data'] as Map)['data'] as List<dynamic>;
          debugPrint('Données extraites via structure data.data');
        }
        // Structure 2: {"success":true,"message":"...","data":[...]}
        else if (vetResponse.containsKey('data') &&
            vetResponse['data'] is List) {
          vets = vetResponse['data'] as List<dynamic>;
          debugPrint('Données extraites via structure data (liste)');
        }
        // Structure 3: Recherche directe dans la réponse
        else {
          for (final key in vetResponse.keys) {
            if (vetResponse[key] is List) {
              vets = vetResponse[key] as List<dynamic>;
              debugPrint('Données extraites via structure $key');
              break;
            }
          }
        }

        debugPrint(
          'Nombre de vétérinaires trouvés (sans paramètre): ${vets.length}',
        );

        if (vets.isNotEmpty) {
          // Afficher tous les vétérinaires pour le débogage
          for (var i = 0; i < vets.length; i++) {
            final vet = vets[i];
            debugPrint(
              'Vétérinaire #$i: '
              'ID: ${vet['_id'] ?? vet['id']}, '
              'Nom: ${vet['prenom']} ${vet['nom']}, '
              'Validé: ${vet['isValidated']}, '
              'Bloqué: ${vet['isBlocked']}',
            );
          }
        } else {
          debugPrint('Aucun vétérinaire trouvé dans la base de données');
        }
      } catch (e) {
        debugPrint('Erreur lors de la récupération sans paramètre: $e');
      }

      // Test pour les maryeurs
      debugPrint('\n=== TEST DES MARYEURS ===');

      // Récupérer directement via le service unifié
      debugPrint('Récupération via UnifiedApiService.getAllMaryeurs()...');
      try {
        final maryeurs = await apiService.getAllMaryeurs();
        debugPrint(
          'Nombre de maryeurs récupérés via le service unifié: ${maryeurs.length}',
        );

        if (maryeurs.isNotEmpty) {
          for (var i = 0; i < maryeurs.length; i++) {
            final mar = maryeurs[i];
            debugPrint(
              'Maryeur #$i: '
              'ID: ${mar.id}, '
              'Nom: ${mar.prenom} ${mar.nom}, '
              'Validé: ${mar.isValidated}, '
              'Bloqué: ${mar.isBlocked}',
            );
          }
        }
      } catch (e) {
        debugPrint('Erreur lors de la récupération via le service unifié: $e');
      }

      // Essayer d'abord avec le paramètre explicite isValidated=true
      debugPrint('\nTentative avec paramètre isValidated=true...');
      try {
        final maryeurResponseWithParam = await apiService.get(
          'maryeurs',
          queryParameters: {'isValidated': 'true'},
        );

        // Afficher la structure complète de la réponse
        debugPrint(
          'Structure de la réponse: ${maryeurResponseWithParam.keys.join(', ')}',
        );

        // Essayer d'extraire les données avec différentes structures
        List<dynamic> maryeursWithParam = [];

        // Structure 1: {"success":true,"message":"...","data":{"data":[...]}}
        if (maryeurResponseWithParam.containsKey('data') &&
            maryeurResponseWithParam['data'] is Map &&
            (maryeurResponseWithParam['data'] as Map).containsKey('data') &&
            (maryeurResponseWithParam['data'] as Map)['data'] is List) {
          maryeursWithParam =
              (maryeurResponseWithParam['data'] as Map)['data']
                  as List<dynamic>;
          debugPrint('Données extraites via structure data.data');
        }
        // Structure 2: {"success":true,"message":"...","data":[...]}
        else if (maryeurResponseWithParam.containsKey('data') &&
            maryeurResponseWithParam['data'] is List) {
          maryeursWithParam = maryeurResponseWithParam['data'] as List<dynamic>;
          debugPrint('Données extraites via structure data (liste)');
        }
        // Structure 3: Recherche directe dans la réponse
        else {
          for (final key in maryeurResponseWithParam.keys) {
            if (maryeurResponseWithParam[key] is List) {
              maryeursWithParam =
                  maryeurResponseWithParam[key] as List<dynamic>;
              debugPrint('Données extraites via structure $key');
              break;
            }
          }
        }

        debugPrint(
          'Nombre de maryeurs validés trouvés (avec paramètre): ${maryeursWithParam.length}',
        );

        if (maryeursWithParam.isNotEmpty) {
          // Afficher tous les maryeurs validés pour le débogage
          for (var i = 0; i < maryeursWithParam.length; i++) {
            final maryeur = maryeursWithParam[i];
            debugPrint(
              'Maryeur #$i: '
              'ID: ${maryeur['_id'] ?? maryeur['id']}, '
              'Nom: ${maryeur['prenom']} ${maryeur['nom']}, '
              'Validé: ${maryeur['isValidated']}, '
              'Bloqué: ${maryeur['isBlocked']}',
            );
          }
        } else {
          debugPrint(
            'Aucun maryeur validé trouvé avec le paramètre isValidated=true',
          );
        }
      } catch (e) {
        debugPrint(
          'Erreur lors de la récupération avec paramètre isValidated=true: $e',
        );
      }

      // Maintenant essayer sans paramètre (comportement par défaut)
      debugPrint('\nTentative sans paramètre...');
      try {
        final maryeurResponse = await apiService.get('maryeurs');

        // Afficher la structure complète de la réponse
        debugPrint(
          'Structure de la réponse: ${maryeurResponse.keys.join(', ')}',
        );

        // Essayer d'extraire les données avec différentes structures
        List<dynamic> maryeurs = [];

        // Structure 1: {"success":true,"message":"...","data":{"data":[...]}}
        if (maryeurResponse.containsKey('data') &&
            maryeurResponse['data'] is Map &&
            (maryeurResponse['data'] as Map).containsKey('data') &&
            (maryeurResponse['data'] as Map)['data'] is List) {
          maryeurs = (maryeurResponse['data'] as Map)['data'] as List<dynamic>;
          debugPrint('Données extraites via structure data.data');
        }
        // Structure 2: {"success":true,"message":"...","data":[...]}
        else if (maryeurResponse.containsKey('data') &&
            maryeurResponse['data'] is List) {
          maryeurs = maryeurResponse['data'] as List<dynamic>;
          debugPrint('Données extraites via structure data (liste)');
        }
        // Structure 3: Recherche directe dans la réponse
        else {
          for (final key in maryeurResponse.keys) {
            if (maryeurResponse[key] is List) {
              maryeurs = maryeurResponse[key] as List<dynamic>;
              debugPrint('Données extraites via structure $key');
              break;
            }
          }
        }

        debugPrint(
          'Nombre de maryeurs trouvés (sans paramètre): ${maryeurs.length}',
        );

        if (maryeurs.isNotEmpty) {
          // Afficher tous les maryeurs pour le débogage
          for (var i = 0; i < maryeurs.length; i++) {
            final maryeur = maryeurs[i];
            debugPrint(
              'Maryeur #$i: '
              'ID: ${maryeur['_id'] ?? maryeur['id']}, '
              'Nom: ${maryeur['prenom']} ${maryeur['nom']}, '
              'Validé: ${maryeur['isValidated']}, '
              'Bloqué: ${maryeur['isBlocked']}',
            );
          }
        } else {
          debugPrint('Aucun maryeur trouvé dans la base de données');
        }
      } catch (e) {
        debugPrint('Erreur lors de la récupération sans paramètre: $e');
      }

      debugPrint('\n=== FIN DES TESTS ===');
    } catch (e) {
      debugPrint('Erreur générale lors du test direct de l\'API: $e');
    }
  }

  @override
  void dispose() {
    // Nous avons supprimé _quantiteController
    _poidController.dispose();
    _temperatureController.dispose();
    _enginController.dispose();
    _zoneController.dispose();
    _emplacementController.dispose();
    super.dispose();
  }

  // Méthode utilitaire pour convertir les IDs MongoDB en string
  String _convertIdToString(dynamic id) {
    if (id is String) {
      return id;
    } else if (id is Map && id.containsKey('buffer')) {
      // Convertir le buffer en ObjectId string
      final buffer = id['buffer'] as Map<String, dynamic>;
      final bytes = List<int>.generate(12, (i) => buffer[i.toString()] as int);
      return bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join('');
    } else {
      return id.toString();
    }
  }

  // Méthode simplifiée pour enregistrer les données du poisson
  Future<void> _saveFishData() async {
    // Vérifier que le formulaire est valide
    if (!_formKey.currentState!.validate()) return;

    // Vérifier que le mareyeur et le vétérinaire sont sélectionnés
    if (_selectedMaryeur == null) {
      setState(() {
        _errorMessage = 'Veuillez sélectionner un mareyeur';
      });
      return;
    }

    if (_selectedVeterinaire == null) {
      setState(() {
        _errorMessage = 'Veuillez sélectionner un vétérinaire';
      });
      return;
    }

    // Afficher un indicateur de chargement
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = 'Enregistrement en cours...';
    });
    debugPrint('Enregistrement en cours...');

    try {
      // Récupérer l'utilisateur connecté (pêcheur)
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null || user is! Pecheur) {
        throw Exception('Utilisateur non autorisé ou non pêcheur');
      }

      debugPrint(
        'Utilisateur connecté: ${user.id}, ${user.nom} ${user.prenom}',
      );

      // Extraire les IDs du mareyeur et du vétérinaire
      final maryeurId = _selectedMaryeur!['_id']?.toString() ?? '';
      final veterinaireId = _selectedVeterinaire!['_id']?.toString() ?? '';

      debugPrint('ID du mareyeur: $maryeurId');
      debugPrint('ID du vétérinaire: $veterinaireId');

      // Vérifier que les IDs sont valides
      if (maryeurId.isEmpty) {
        throw Exception(
          'ID du mareyeur invalide. Veuillez sélectionner un autre mareyeur.',
        );
      }
      if (veterinaireId.isEmpty) {
        throw Exception(
          'ID du vétérinaire invalide. Veuillez sélectionner un autre vétérinaire.',
        );
      }

      // Télécharger l'image sur le serveur
      setState(() {
        _successMessage = "Téléchargement de l'image en cours...";
      });

      debugPrint('Téléchargement de l\'image...');
      final imageUrl = await UnifiedApiService().uploadImage(widget.imageFile);
      debugPrint('Image téléchargée: $imageUrl');

      setState(() {
        _successMessage =
            "Image téléchargée avec succès. Création de la prise en cours...";
      });

      // Préparer les données pour la date
      final now = DateTime.now();
      final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');

      // Approche simplifiée: créer directement un lot sans passer par la prise
      // Cela simplifie le processus car chaque photo ne contient qu'une seule espèce de poisson
      debugPrint('Utilisation de l\'approche simplifiée pour créer le lot...');

      // Préparer les données du lot avec tous les champs requis - SIMPLIFIÉ
      final lotData = {
        // Identifiants et métadonnées
        'identifiant': 'LOT-${now.millisecondsSinceEpoch}',
        'nom': 'Lot de ${widget.especeNom}',
        'dateSoumission': dateFormat.format(now),

        // Informations sur le poisson
        'photo': imageUrl,
        'poids': double.parse(_poidController.text),
        'temperature': double.parse(_temperatureController.text),
        'quantite': 1,

        // Informations sur l'espèce - Version simplifiée
        'especeNom': widget.especeNom,

        // Informations sur la pêche
        'engin': _enginController.text,
        'zone': _zoneController.text,
        'lieu':
            _emplacementController.text.isNotEmpty
                ? _emplacementController.text
                : 'Non spécifié',

        // Acteurs concernés - Nous utiliserons les IDs MongoDB directement dans la méthode d'envoi
        'pecheur': user.id,
        'user': user.id,

        // Les IDs du vétérinaire et du mareyeur seront définis plus tard
        // avec les valeurs correctes de _selectedVeterinaire et _selectedMaryeur

        // Statuts (obligatoires)
        'status': false,
        'test': false,
        'vendu': false,
        'isValidated': false,
      };

      // L'emplacement est déjà inclus dans les données du lot

      debugPrint('Création du lot avec les données: $lotData');

      // Créer le lot directement via l'API avec gestion d'erreur améliorée
      debugPrint('Envoi des données au serveur...');
      Map<String, dynamic> lotResponse;

      try {
        // Extraire et valider les IDs MongoDB
        final veterinaireId = _selectedVeterinaire!['_id']?.toString() ?? '';
        final maryeurId = _selectedMaryeur!['_id']?.toString() ?? '';

        debugPrint(
          'Envoi des données au serveur avec les IDs: vétérinaire=$veterinaireId, mareyeur=$maryeurId',
        );

        if (veterinaireId.isEmpty || maryeurId.isEmpty) {
          throw Exception('IDs du vétérinaire ou du mareyeur invalides');
        }

        debugPrint('ID vétérinaire: $veterinaireId');
        debugPrint('ID mareyeur: $maryeurId');

        // Mettre à jour les données du lot avec les IDs corrects
        lotData['veterinaire'] = veterinaireId;
        lotData['maryeur'] = maryeurId;

        debugPrint('=== DONNÉES FINALES DU LOT ===');
        debugPrint('Données complètes à envoyer: ${json.encode(lotData)}');

        lotResponse = await UnifiedApiService().post('lots', lotData);
        debugPrint('Réponse de création du lot: $lotResponse');

        // Vérifier si la réponse contient une erreur
        if (lotResponse.containsKey('error')) {
          debugPrint(
            'Erreur détectée dans la réponse: ${lotResponse['error']}',
          );
          if (lotResponse.containsKey('details')) {
            debugPrint('Détails de l\'erreur: ${lotResponse['details']}');
          }
          throw Exception('Erreur du serveur: ${lotResponse['error']}');
        }
      } catch (apiError) {
        debugPrint('Erreur lors de l\'appel API: $apiError');
        debugPrint('Type d\'erreur: ${apiError.runtimeType}');
        debugPrint('Stack trace: ${StackTrace.current}');

        // Essayer une approche alternative avec seulement les champs essentiels
        debugPrint('Tentative avec un ensemble de champs minimal essentiel...');

        // Utiliser directement les IDs MongoDB des vétérinaires et mareyeurs
        final veterinaireId = _selectedVeterinaire!['_id'].toString();
        final maryeurId = _selectedMaryeur!['_id'].toString();

        final simplifiedData = {
          // Champs absolument essentiels
          'identifiant': lotData['identifiant'],
          'nom': lotData['nom'] ?? 'Lot de poisson',
          'photo': lotData['photo'],
          'poids': lotData['poids'],
          'especeNom': lotData['especeNom'] ?? 'Poisson',
          'pecheur': lotData['pecheur'],
          'user': lotData['user'],
          'veterinaireId': veterinaireId,
          'maryeurId': maryeurId,
          // Statuts obligatoires
          'status': false,
          'test': false,
          'vendu': false,
          'isValidated': false,
        };

        debugPrint(
          'Tentative alternative avec les IDs MongoDB: vétérinaire=$veterinaireId, mareyeur=$maryeurId',
        );
        lotResponse = await UnifiedApiService().post('lots', simplifiedData);
        debugPrint('Réponse de la tentative alternative: $lotResponse');

        // Vérifier si la réponse contient une erreur
        if (lotResponse.containsKey('error')) {
          debugPrint(
            'Erreur détectée dans la réponse alternative: ${lotResponse['error']}',
          );
          if (lotResponse.containsKey('details')) {
            debugPrint(
              'Détails de l\'erreur alternative: ${lotResponse['details']}',
            );
          }
          throw Exception(
            'Erreur du serveur (tentative alternative): ${lotResponse['error']}',
          );
        }
      }

      // Extraire l'ID du lot créé
      String lotId = '';
      if (lotResponse.containsKey('data')) {
        var data = lotResponse['data'];
        lotId = data['_id']?.toString() ?? data['id']?.toString() ?? '';
      }

      if (lotId.isEmpty) {
        throw Exception('Impossible de créer le lot. Veuillez réessayer.');
      }

      debugPrint('Lot créé avec succès: $lotId');

      // ✅ SUPPRIMÉ : Le double PATCH n'est plus nécessaire car les associations
      // sont maintenant correctement incluses dans la création initiale

      // Notifier le vétérinaire manuellement pour s'assurer qu'il reçoit la notification
      final veterinaire = _selectedVeterinaire!['_id'];
      debugPrint(
        'Envoi de notification au vétérinaire $veterinaire pour le lot $lotId',
      );
      try {
        await UnifiedApiService().post('notifications', {
          'destinataireId': veterinaire,
          'destinataireType': 'Veterinaire',
          'titre': 'Nouveau lot à valider',
          'contenu':
              'Un nouveau lot de ${widget.especeNom} vous a été assigné pour validation.',
          'type': 'info',
          'reference': lotId,
          'referenceModel': 'Lot',
          'urlAction': '/veterinaire/lots/$lotId',
        });
        debugPrint('Notification envoyée avec succès');
      } catch (notifError) {
        debugPrint('Erreur lors de l\'envoi de la notification: $notifError');
        // Ne pas échouer si la notification échoue
      }

      // Afficher un message de succès et retourner à l'écran principal
      setState(() {
        _isLoading = false;
        _successMessage = "Lot créé avec succès!";
      });

      if (!mounted) return;

      // Afficher un message de succès
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: const [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 8),
              Text('Poisson enregistré avec succès!'),
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.secondary,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          duration: const Duration(seconds: 3),
        ),
      );

      // Retourner à l'écran principal
      Navigator.of(context).popUntil((route) => route.isFirst);
    } catch (e) {
      debugPrint('Erreur lors de l\'enregistrement: $e');
      debugPrint('Type d\'erreur: ${e.runtimeType}');
      debugPrint('Stack trace: ${StackTrace.current}');

      // Formater le message d'erreur pour qu'il soit plus convivial
      String errorMessage = 'Erreur lors de l\'enregistrement';

      if (e.toString().contains('Pêcheur, mareyeur et nom sont requis')) {
        errorMessage = 'Veuillez remplir tous les champs obligatoires';
      } else if (e.toString().contains('mareyeur')) {
        errorMessage =
            'Erreur avec le mareyeur. Veuillez réessayer ou contacter l\'administrateur';
      } else if (e.toString().contains('vétérinaire') ||
          e.toString().contains('veterinaire')) {
        errorMessage =
            'Erreur avec le vétérinaire. Veuillez réessayer ou contacter l\'administrateur';
      } else if (e.toString().contains('connexion') ||
          e.toString().contains('network') ||
          e.toString().contains('SocketException') ||
          e.toString().contains('timeout')) {
        errorMessage =
            'Problème de connexion au serveur. Vérifiez votre connexion internet.';
      } else if (e.toString().contains('Impossible de créer la prise')) {
        errorMessage =
            'Erreur lors de la création de la prise. Veuillez réessayer.';
      } else if (e.toString().contains('Échec de la création du lot')) {
        errorMessage = 'Erreur lors de la création du lot. Veuillez réessayer.';
      } else if (e.toString().contains('obligatoires')) {
        errorMessage =
            'Tous les champs obligatoires doivent être renseignés. Veuillez vérifier les informations saisies.';
      } else if (e.toString().contains('Erreur du serveur')) {
        errorMessage = e.toString().replaceAll('Exception: ', '');
      }

      // Ajouter des logs détaillés pour le débogage
      debugPrint('Message d\'erreur formaté: $errorMessage');
      debugPrint('Erreur originale: $e');

      setState(() {
        _errorMessage = errorMessage;
        _isLoading = false;
        _successMessage = null;
      });

      // Afficher un message d'erreur
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text(errorMessage)),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // Méthode pour rafraîchir toutes les données
  Future<void> _refreshAllData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // Afficher un message de chargement
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Actualisation des données en cours...'),
          duration: Duration(seconds: 2),
        ),
      );
    }

    // Charger les données
    await _loadMaryeurs();
    await _loadVeterinaires();

    // Afficher un message de succès
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Données actualisées avec succès'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  // Méthode pour exécuter un diagnostic complet
  Future<void> _runDiagnostic() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // Afficher un message de chargement
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Diagnostic en cours...'),
          duration: Duration(seconds: 2),
        ),
      );
    }

    try {
      // Vérifier la connectivité au serveur
      final apiService = UnifiedApiService();
      final isConnected = await apiService.checkServerConnectivity();

      if (!isConnected) {
        setState(() {
          _errorMessage =
              'Impossible de se connecter au serveur. Vérifiez que le serveur backend est démarré et que votre appareil est sur le même réseau.';
          _isLoading = false;
        });
        return;
      }

      debugPrint('Connectivité au serveur vérifiée: OK');

      // Récupérer directement les mareyeurs et vétérinaires sans filtre
      debugPrint('Récupération directe des mareyeurs...');
      final maryeursResponse = await apiService.get('maryeurs');
      debugPrint('Récupération directe des vétérinaires...');
      final veterinairesResponse = await apiService.get('veterinaires');

      // Extraire les données
      debugPrint(
        'Structure de la réponse mareyeurs: ${maryeursResponse.keys.join(', ')}',
      );
      debugPrint(
        'Structure de la réponse vétérinaires: ${veterinairesResponse.keys.join(', ')}',
      );

      List<dynamic> maryeursData = [];
      if (maryeursResponse.containsKey('data')) {
        if (maryeursResponse['data'] is List) {
          maryeursData = maryeursResponse['data'] as List<dynamic>;
          debugPrint('Données mareyeurs extraites directement de data (liste)');
        } else if (maryeursResponse['data'] is Map &&
            (maryeursResponse['data'] as Map).containsKey('data')) {
          maryeursData =
              (maryeursResponse['data'] as Map)['data'] as List<dynamic>;
          debugPrint('Données mareyeurs extraites de data.data');
        }
      }

      List<dynamic> veterinairesData = [];
      if (veterinairesResponse.containsKey('data')) {
        if (veterinairesResponse['data'] is List) {
          veterinairesData = veterinairesResponse['data'] as List<dynamic>;
          debugPrint(
            'Données vétérinaires extraites directement de data (liste)',
          );
        } else if (veterinairesResponse['data'] is Map &&
            (veterinairesResponse['data'] as Map).containsKey('data')) {
          veterinairesData =
              (veterinairesResponse['data'] as Map)['data'] as List<dynamic>;
          debugPrint('Données vétérinaires extraites de data.data');
        }
      }

      // Essayer aussi avec getAllMaryeurs et getAllVeterinaires
      debugPrint('\nRécupération via getAllMaryeurs()...');
      final maryeursObjets = await apiService.getAllMaryeurs();
      debugPrint(
        'Nombre de mareyeurs récupérés via getAllMaryeurs(): ${maryeursObjets.length}',
      );

      debugPrint('\nRécupération via getAllVeterinaires()...');
      final veterinairesObjets = await apiService.getAllVeterinaires();
      debugPrint(
        'Nombre de vétérinaires récupérés via getAllVeterinaires(): ${veterinairesObjets.length}',
      );

      // Afficher les résultats
      debugPrint('=== DIAGNOSTIC COMPLET ===');
      debugPrint('URL de base de l\'API: ${apiService.getBaseUrl()}');
      debugPrint('Nombre total de mareyeurs: ${maryeursData.length}');
      debugPrint('Nombre total de vétérinaires: ${veterinairesData.length}');

      // Filtrer manuellement
      final filteredMaryeurs =
          maryeursData
              .where(
                (item) =>
                    item is Map &&
                    (item['isValidated'] == true || item['isValid'] == true) &&
                    (item['isBlocked'] == false || item['isBlocked'] == null),
              )
              .toList();

      final filteredVeterinaires =
          veterinairesData
              .where(
                (item) =>
                    item is Map &&
                    (item['isValidated'] == true || item['isValid'] == true) &&
                    (item['isBlocked'] == false || item['isBlocked'] == null),
              )
              .toList();

      debugPrint(
        'Nombre de mareyeurs validés et non bloqués: ${filteredMaryeurs.length}',
      );
      debugPrint(
        'Nombre de vétérinaires validés et non bloqués: ${filteredVeterinaires.length}',
      );

      // Afficher les détails des mareyeurs validés
      if (filteredMaryeurs.isNotEmpty) {
        debugPrint('\n=== MAREYEURS VALIDÉS ===');
        for (var i = 0; i < filteredMaryeurs.length; i++) {
          final maryeur = filteredMaryeurs[i];
          debugPrint(
            'Mareyeur #$i: ${maryeur['prenom']} ${maryeur['nom']}, '
            'ID: ${maryeur['_id'] ?? maryeur['id']}, '
            'Validé: ${maryeur['isValidated']}, '
            'Bloqué: ${maryeur['isBlocked']}',
          );
        }
      }

      // Afficher les détails des vétérinaires validés
      if (filteredVeterinaires.isNotEmpty) {
        debugPrint('\n=== VÉTÉRINAIRES VALIDÉS ===');
        for (var i = 0; i < filteredVeterinaires.length; i++) {
          final veterinaire = filteredVeterinaires[i];
          debugPrint(
            'Vétérinaire #$i: ${veterinaire['prenom']} ${veterinaire['nom']}, '
            'ID: ${veterinaire['_id'] ?? veterinaire['id']}, '
            'Validé: ${veterinaire['isValidated']}, '
            'Bloqué: ${veterinaire['isBlocked']}',
          );
        }
      }

      // Mettre à jour l'interface
      setState(() {
        _isLoading = false;
        if (filteredMaryeurs.isEmpty && filteredVeterinaires.isEmpty) {
          _errorMessage =
              'Aucun mareyeur ni vétérinaire validé trouvé dans la base de données.';
        } else if (filteredMaryeurs.isEmpty) {
          _errorMessage =
              'Aucun mareyeur validé trouvé dans la base de données.';
        } else if (filteredVeterinaires.isEmpty) {
          _errorMessage =
              'Aucun vétérinaire validé trouvé dans la base de données.';
        } else {
          // Recharger les données normalement
          _loadMaryeurs();
          _loadVeterinaires();
        }
      });

      // Afficher un message de succès
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Diagnostic terminé. ${filteredMaryeurs.length} mareyeurs et ${filteredVeterinaires.length} vétérinaires validés trouvés.',
            ),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      debugPrint('Erreur lors du diagnostic: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Erreur lors du diagnostic: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Détails du poisson'),
        elevation: 0,
        actions: [
          // Bouton de rafraîchissement
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Actualiser les données',
            onPressed: _refreshAllData,
          ),
          // Bouton de diagnostic
          IconButton(
            icon: const Icon(Icons.bug_report),
            tooltip: 'Exécuter un diagnostic',
            onPressed: _runDiagnostic,
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: _responsiveService.adaptivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: _animationService.staggeredList([
              // Bouton de rafraîchissement global
              if (_errorMessage != null)
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: theme.colorScheme.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: theme.colorScheme.error,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Problème de chargement des données',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.colorScheme.error,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _errorMessage ??
                            'Une erreur est survenue lors du chargement des données.',
                        style: theme.textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: CustomButton.filled(
                          text: 'Actualiser toutes les données',
                          icon: Icons.refresh,
                          onPressed: _refreshAllData,
                          color: theme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),

              // Fish identification result
              SeaCard(
                elevated: true,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.secondary.withValues(
                              alpha: 0.1,
                            ),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.check_circle,
                            color: theme.colorScheme.secondary,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Espèce identifiée avec succès',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.secondary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.file(
                            widget.imageFile,
                            width: 120,
                            height: 120,
                            fit: BoxFit.cover,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.especeNom,
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: primaryColor,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                widget.especeNom,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Identification réussie avec notre système d\'IA',
                                style: theme.textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Fish details form
              SeaSectionHeader(
                title: 'Informations complémentaires',
                icon: Icons.edit_note,
                subtitle:
                    'Veuillez compléter les informations sur votre capture',
              ),

              SeaCard(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Nous avons supprimé le champ de quantité pour ne garder que le poids

                      // Weight
                      TextFormField(
                        controller: _poidController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'Poids (kg)',
                          hintText: 'Entrez le poids total en kg',
                          prefixIcon: Icon(Icons.scale),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez entrer le poids';
                          }
                          if (double.tryParse(value) == null) {
                            return 'Veuillez entrer un nombre valide';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Temperature
                      TextFormField(
                        controller: _temperatureController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'Température (°C)',
                          hintText: 'Entrez la température de conservation',
                          prefixIcon: Icon(Icons.thermostat),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez entrer la température';
                          }
                          if (double.tryParse(value) == null) {
                            return 'Veuillez entrer un nombre valide';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Fishing method
                      DropdownButtonFormField<String>(
                        value: _enginController.text,
                        decoration: const InputDecoration(
                          labelText: 'Méthode de pêche',
                          prefixIcon: Icon(Icons.sailing),
                        ),
                        items:
                            _methodesDepeche.map((String method) {
                              return DropdownMenuItem<String>(
                                value: method,
                                child: Text(method),
                              );
                            }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            setState(() {
                              _enginController.text = newValue;
                            });
                          }
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez sélectionner une méthode de pêche';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Fishing zone
                      DropdownButtonFormField<String>(
                        value: _zoneController.text,
                        decoration: const InputDecoration(
                          labelText: 'Zone de pêche',
                          prefixIcon: Icon(Icons.map),
                        ),
                        items:
                            _zonesDepeche.map((String zone) {
                              return DropdownMenuItem<String>(
                                value: zone,
                                child: Text(zone),
                              );
                            }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            setState(() {
                              _zoneController.text = newValue;
                            });
                          }
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez sélectionner une zone de pêche';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Sélection du vétérinaire
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Vétérinaire destinataire',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Sélectionnez un vétérinaire qui validera votre prise',
                                style: theme.textTheme.bodyMedium,
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Affichage conditionnel selon l'état du chargement
                          if (_isLoading && _veterinaires.isEmpty)
                            Center(
                              child: Column(
                                children: [
                                  const CircularProgressIndicator(),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Chargement des vétérinaires...',
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                            )
                          else if (_veterinaires.isEmpty)
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.error.withValues(
                                  alpha: 0.1,
                                ),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: theme.colorScheme.error.withValues(
                                    alpha: 0.3,
                                  ),
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.warning_amber_rounded,
                                        color: theme.colorScheme.error,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          'Aucun vétérinaire disponible dans la liste',
                                          style: theme.textTheme.titleMedium
                                              ?.copyWith(
                                                color: theme.colorScheme.error,
                                                fontWeight: FontWeight.bold,
                                              ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Veuillez demander à des vétérinaires de créer un compte dans l\'application ou vérifier que les comptes existants sont validés par l\'administrateur.',
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                  const SizedBox(height: 16),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: CustomButton.outline(
                                          text: 'Actualiser la liste',
                                          icon: Icons.refresh,
                                          onPressed: _loadVeterinaires,
                                          color: theme.primaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            )
                          else
                            DropdownButtonFormField<Map<String, dynamic>>(
                              value: _selectedVeterinaire,
                              decoration: const InputDecoration(
                                labelText: 'Sélectionnez un vétérinaire',
                                prefixIcon: Icon(Icons.medical_services),
                                hintText: 'Choisir un vétérinaire',
                              ),
                              items:
                                  _veterinaires.map((veterinaire) {
                                    final nom = veterinaire['nom'] ?? '';
                                    final prenom = veterinaire['prenom'] ?? '';
                                    final port = veterinaire['port'] ?? '';
                                    final isValidated =
                                        veterinaire['isValidated'] ?? false;

                                    // Ajouter un indicateur pour les utilisateurs non validés
                                    final nomComplet =
                                        isValidated
                                            ? '$prenom $nom${port.isNotEmpty ? ' ($port)' : ''}'
                                            : '$prenom $nom${port.isNotEmpty ? ' ($port)' : ''} [NON VALIDÉ]';

                                    // Afficher les détails pour le débogage
                                    debugPrint(
                                      'Vétérinaire dans dropdown: $nomComplet',
                                    );
                                    debugPrint(
                                      '  ID: ${veterinaire['_id'] ?? veterinaire['id']}',
                                    );
                                    debugPrint(
                                      '  isValidated: ${veterinaire['isValidated']}',
                                    );

                                    return DropdownMenuItem<
                                      Map<String, dynamic>
                                    >(
                                      value: veterinaire,
                                      child: Text(nomComplet),
                                    );
                                  }).toList(),
                              onChanged: (Map<String, dynamic>? newValue) {
                                if (newValue != null) {
                                  setState(() {
                                    _selectedVeterinaire = newValue;
                                  });
                                }
                              },
                              validator: (value) {
                                if (value == null) {
                                  return 'Veuillez sélectionner un vétérinaire';
                                }
                                return null;
                              },
                              isExpanded: true,
                            ),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // Sélection du mareyeur
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Mareyeur destinataire',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Sélectionnez un mareyeur à qui envoyer votre prise',
                                style: theme.textTheme.bodyMedium,
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Affichage conditionnel selon l'état du chargement
                          if (_isLoading && _maryeurs.isEmpty)
                            Center(
                              child: Column(
                                children: [
                                  const CircularProgressIndicator(),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Chargement des mareyeurs...',
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                            )
                          else if (_maryeurs.isEmpty)
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.error.withValues(
                                  alpha: 0.1,
                                ),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: theme.colorScheme.error.withValues(
                                    alpha: 0.3,
                                  ),
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.warning_amber_rounded,
                                        color: theme.colorScheme.error,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          'Aucun mareyeur disponible dans la liste',
                                          style: theme.textTheme.titleMedium
                                              ?.copyWith(
                                                color: theme.colorScheme.error,
                                                fontWeight: FontWeight.bold,
                                              ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Veuillez demander à des mareyeurs de créer un compte dans l\'application ou vérifier que les comptes existants sont validés par l\'administrateur.',
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                  const SizedBox(height: 16),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: CustomButton.outline(
                                          text: 'Actualiser la liste',
                                          icon: Icons.refresh,
                                          onPressed: _loadMaryeurs,
                                          color: theme.primaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            )
                          else
                            DropdownButtonFormField<Map<String, dynamic>>(
                              value: _selectedMaryeur,
                              decoration: const InputDecoration(
                                labelText: 'Sélectionnez un mareyeur',
                                prefixIcon: Icon(Icons.business),
                                hintText: 'Choisir un mareyeur',
                              ),
                              items:
                                  _maryeurs.map((maryeur) {
                                    final nom = maryeur['nom'] ?? '';
                                    final prenom = maryeur['prenom'] ?? '';
                                    final port = maryeur['port'] ?? '';
                                    final isValidated =
                                        maryeur['isValidated'] ?? false;

                                    // Ajouter un indicateur pour les utilisateurs non validés
                                    final nomComplet =
                                        isValidated
                                            ? '$prenom $nom${port.isNotEmpty ? ' ($port)' : ''}'
                                            : '$prenom $nom${port.isNotEmpty ? ' ($port)' : ''} [NON VALIDÉ]';

                                    // Afficher les détails pour le débogage
                                    debugPrint(
                                      'Mareyeur dans dropdown: $nomComplet',
                                    );
                                    debugPrint(
                                      '  ID: ${maryeur['_id'] ?? maryeur['id']}',
                                    );
                                    debugPrint(
                                      '  isValidated: ${maryeur['isValidated']}',
                                    );

                                    return DropdownMenuItem<
                                      Map<String, dynamic>
                                    >(value: maryeur, child: Text(nomComplet));
                                  }).toList(),
                              onChanged: (Map<String, dynamic>? newValue) {
                                if (newValue != null) {
                                  setState(() {
                                    _selectedMaryeur = newValue;
                                  });
                                }
                              },
                              validator: (value) {
                                if (value == null) {
                                  return 'Veuillez sélectionner un mareyeur';
                                }
                                return null;
                              },
                              isExpanded: true,
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Vétérinaire sélectionné
              if (_selectedVeterinaire != null) ...[
                SeaSectionHeader(
                  title: 'Vétérinaire sélectionné',
                  icon: Icons.medical_services,
                  subtitle:
                      'Informations sur le vétérinaire qui validera votre prise',
                ),

                SeaCard(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color:
                                  (_selectedVeterinaire!['isValidated'] ??
                                          false)
                                      ? theme.colorScheme.secondary.withValues(
                                        alpha: 0.1,
                                      )
                                      : theme.colorScheme.error.withValues(
                                        alpha: 0.1,
                                      ),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Icon(
                                (_selectedVeterinaire!['isValidated'] ?? false)
                                    ? Icons.medical_services
                                    : Icons.warning,
                                color:
                                    (_selectedVeterinaire!['isValidated'] ??
                                            false)
                                        ? theme.colorScheme.secondary
                                        : theme.colorScheme.error,
                                size: 30,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${_selectedVeterinaire!['prenom'] ?? ''} ${_selectedVeterinaire!['nom'] ?? ''}',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color:
                                        (_selectedVeterinaire!['isValidated'] ??
                                                false)
                                            ? null
                                            : theme.colorScheme.error,
                                  ),
                                ),
                                if (_selectedVeterinaire!['matricule'] != null)
                                  Text(
                                    'Matricule: ${_selectedVeterinaire!['matricule']}',
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                if (_selectedVeterinaire!['telephone'] != null)
                                  Text(
                                    'Tél: ${_selectedVeterinaire!['telephone']}',
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                if (!(_selectedVeterinaire!['isValidated'] ??
                                    false))
                                  Container(
                                    margin: const EdgeInsets.only(top: 8),
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.error.withValues(
                                        alpha: 0.1,
                                      ),
                                      borderRadius: BorderRadius.circular(4),
                                      border: Border.all(
                                        color: theme.colorScheme.error
                                            .withValues(alpha: 0.3),
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.warning,
                                          color: theme.colorScheme.error,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            'Ce vétérinaire n\'est pas encore validé par l\'administrateur',
                                            style: theme.textTheme.bodySmall
                                                ?.copyWith(
                                                  color:
                                                      theme.colorScheme.error,
                                                ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],

              // Mareyeur sélectionné
              if (_selectedMaryeur != null) ...[
                SeaSectionHeader(
                  title: 'Mareyeur sélectionné',
                  icon: Icons.business,
                  subtitle:
                      'Informations sur le mareyeur qui recevra votre prise',
                ),

                SeaCard(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color:
                                  (_selectedMaryeur!['isValidated'] ?? false)
                                      ? theme.colorScheme.primary.withValues(
                                        alpha: 0.1,
                                      )
                                      : theme.colorScheme.error.withValues(
                                        alpha: 0.1,
                                      ),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Icon(
                                (_selectedMaryeur!['isValidated'] ?? false)
                                    ? Icons.person
                                    : Icons.warning,
                                color:
                                    (_selectedMaryeur!['isValidated'] ?? false)
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.error,
                                size: 30,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${_selectedMaryeur!['prenom'] ?? ''} ${_selectedMaryeur!['nom'] ?? ''}',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color:
                                        (_selectedMaryeur!['isValidated'] ??
                                                false)
                                            ? null
                                            : theme.colorScheme.error,
                                  ),
                                ),
                                if (_selectedMaryeur!['matricule'] != null)
                                  Text(
                                    'Matricule: ${_selectedMaryeur!['matricule']}',
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                if (_selectedMaryeur!['telephone'] != null)
                                  Text(
                                    'Tél: ${_selectedMaryeur!['telephone']}',
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                if (!(_selectedMaryeur!['isValidated'] ??
                                    false))
                                  Container(
                                    margin: const EdgeInsets.only(top: 8),
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.error.withValues(
                                        alpha: 0.1,
                                      ),
                                      borderRadius: BorderRadius.circular(4),
                                      border: Border.all(
                                        color: theme.colorScheme.error
                                            .withValues(alpha: 0.3),
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.warning,
                                          color: theme.colorScheme.error,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            'Ce mareyeur n\'est pas encore validé par l\'administrateur',
                                            style: theme.textTheme.bodySmall
                                                ?.copyWith(
                                                  color:
                                                      theme.colorScheme.error,
                                                ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],

              // Emplacement (optionnel)
              SeaSectionHeader(
                title: 'Emplacement (optionnel)',
                icon: Icons.location_on,
                subtitle:
                    'Vous pouvez indiquer l\'emplacement de pêche si vous le souhaitez',
              ),

              SeaCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      controller: _emplacementController,
                      decoration: const InputDecoration(
                        labelText: 'Emplacement de pêche',
                        hintText: 'Ex: Port de Sfax, Côte de Kerkennah...',
                        prefixIcon: Icon(Icons.location_on),
                      ),
                      // Pas de validation car c'est un champ optionnel
                    ),
                  ],
                ),
              ),

              // Error or success messages
              if (_errorMessage != null)
                _animationService.shake(
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.colorScheme.error.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: theme.colorScheme.error,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _errorMessage!,
                            style: TextStyle(color: theme.colorScheme.error),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              if (_successMessage != null)
                _animationService.fadeIn(
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.secondary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.colorScheme.secondary.withValues(
                          alpha: 0.3,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: theme.colorScheme.secondary,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _successMessage!,
                            style: TextStyle(
                              color: theme.colorScheme.secondary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Afficher le message d'erreur s'il y en a un
              if (_errorMessage != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: theme.colorScheme.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: theme.colorScheme.error),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(
                            color: theme.colorScheme.error,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Save button
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: CustomButton.filled(
                  text:
                      _isLoading
                          ? 'Enregistrement...'
                          : 'Enregistrer et soumettre',
                  icon: _isLoading ? null : Icons.save,
                  onPressed:
                      (_isLoading ||
                              _selectedMaryeur == null ||
                              _selectedVeterinaire == null)
                          ? null
                          : _saveFishData,
                  isLoading: _isLoading,
                  color: theme.primaryColor,
                  size: CustomButtonSize.large,
                ),
              ),
              if ((_selectedMaryeur == null || _selectedVeterinaire == null) &&
                  !_isLoading) ...[
                const SizedBox(height: 8),
                Text(
                  'Vous devez sélectionner un mareyeur et un vétérinaire pour enregistrer votre prise.',
                  style: TextStyle(
                    color: theme.colorScheme.error,
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
              const SizedBox(height: 16),
            ]),
          ),
        ),
      ),
    );
  }
}
